"use client";

import { motion } from "framer-motion";
import { Leaf, Target, Factory, Truck, Zap, Recycle } from "lucide-react";

export function SustainabilityContent() {
  return (
    <section className="py-32 bg-white">
      <div className="container mx-auto px-6">
        <div className="max-w-6xl mx-auto">
          {/* Main Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-20"
          >
            <div className="bg-slate-50 border border-slate-100 rounded-2xl p-8 md:p-12">
              <p className="text-lg text-slate-700 leading-relaxed font-light mb-8">
                <PERSON><PERSON><PERSON>'s sustainability approach is to operate within the realms of real time practices as much possible. 
                We use processes which are environmental friendly by contributing towards global climate consciousness 
                roadmap for healthier and greener planet.
              </p>
            </div>
          </motion.div>

          {/* Net Zero Carbon Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="mb-20"
          >
            <div className="text-center mb-12">
              <div className="w-16 h-16 bg-slate-900 rounded-xl flex items-center justify-center mx-auto mb-6">
                <Target className="h-8 w-8 text-white" />
              </div>
              <h2 className="text-3xl md:text-4xl font-light text-slate-900 mb-6 tracking-tight">
                Net Zero Carbon <span className="font-normal text-slate-600">Project Approach</span>
              </h2>
              <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto mb-8"></div>
            </div>

            <div className="bg-slate-50 border border-slate-100 rounded-2xl p-8 md:p-12 mb-12">
              <p className="text-lg text-slate-700 leading-relaxed font-light mb-8">
                Aim is to achieve nearest to Net Zero Carbon in a project contributing steps towards the global drive 
                for cleaner planet. We approach keeping the following in perspective:
              </p>

              {/* Three Pillars */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="w-14 h-14 bg-slate-800 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <Factory className="h-7 w-7 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-slate-900 mb-4">Direct Emissions</h3>
                  <p className="text-slate-600 font-light leading-relaxed">
                    Direct emissions from the project's operations such as company vehicles or manufacturing units
                  </p>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="w-14 h-14 bg-slate-800 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <Zap className="h-7 w-7 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-slate-900 mb-4">Indirect Operations</h3>
                  <p className="text-slate-600 font-light leading-relaxed">
                    Indirect emissions from project's operations such as power source generated upon fossil fuels
                  </p>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="w-14 h-14 bg-slate-800 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <Truck className="h-7 w-7 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-slate-900 mb-4">Supply Chain</h3>
                  <p className="text-slate-600 font-light leading-relaxed">
                    Indirect emissions from project's supply chains such as shipping, business travel, 
                    raw material extraction and installed elements manufacturing
                  </p>
                </motion.div>
              </div>
            </div>
          </motion.div>

          {/* Carbon Footprint Reduction */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <div className="bg-slate-900 rounded-3xl p-12 md:p-16">
              <div className="w-16 h-16 bg-slate-700 rounded-xl flex items-center justify-center mx-auto mb-8">
                <Recycle className="h-8 w-8 text-white" />
              </div>
              <blockquote className="text-2xl md:text-3xl font-light text-white leading-relaxed mb-8">
                "It is essential to take measures in Reducing Carbon Footprint as much as practically possible."
              </blockquote>
              <div className="w-16 h-px bg-slate-600 mx-auto mb-6"></div>
              <p className="text-slate-400 font-light">KleverCo Sustainability Commitment</p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
