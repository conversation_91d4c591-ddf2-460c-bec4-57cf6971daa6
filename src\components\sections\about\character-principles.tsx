"use client";

import { motion } from "framer-motion";
import { Shield, Eye, Zap, DollarSign, Scale, Heart, FileText } from "lucide-react";

const principles = [
  {
    title: "Confidentiality",
    description: "We believe in Confidentiality deem necessary in our practice by maintaining privacy.",
    icon: Shield,
  },
  {
    title: "Transparency", 
    description: "We practice Transparency by full disclosure of the transaction to everyone involved.",
    icon: Eye,
  },
  {
    title: "Consistent",
    description: "We are Consistent in our approach with rigorous experience and diversified exposure.",
    icon: Zap,
  },
  {
    title: "Efficient Solutions",
    description: "We present cost effective and swift solutions according to the need of a specific business.",
    icon: DollarSign,
  },
  {
    title: "Respect Laws",
    description: "We respect International Laws and Regulations applicable to our transactions.",
    icon: Scale,
  },
  {
    title: "Human Rights",
    description: "We highly value Human Rights above everything else.",
    icon: Heart,
  },
  {
    title: "Policies and Regulations",
    description: "We abide by Policies and Regulations governing our associates, partners and clientele.",
    icon: FileText,
  }
];

export function CharacterPrinciples() {
  return (
    <section className="py-32 bg-white">
      <div className="container mx-auto px-6">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-6 tracking-tight">
              Core <span className="font-normal text-slate-600">Principles</span>
            </h2>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto mb-8"></div>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed font-light">
              These principles form the foundation of our business philosophy and guide every decision we make.
            </p>
          </motion.div>

          {/* Principles Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12">
            {principles.map((principle, index) => {
              const Icon = principle.icon;
              
              return (
                <motion.div
                  key={principle.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="group"
                >
                  <div className="bg-slate-50 border border-slate-100 rounded-2xl p-8 h-full hover:shadow-lg hover:border-slate-200 transition-all duration-300">
                    {/* Icon */}
                    <div className="w-14 h-14 bg-slate-900 rounded-xl flex items-center justify-center mb-6 group-hover:bg-slate-800 transition-colors duration-300">
                      <Icon className="h-7 w-7 text-white" />
                    </div>

                    {/* Content */}
                    <h3 className="text-xl font-semibold text-slate-900 mb-4 group-hover:text-slate-800 transition-colors duration-300">
                      {principle.title}
                    </h3>
                    <p className="text-slate-600 leading-relaxed font-light">
                      {principle.description}
                    </p>
                  </div>
                </motion.div>
              );
            })}
          </div>

          {/* Bottom Statement */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-center mt-20"
          >
            <div className="bg-slate-900 rounded-3xl p-12 md:p-16">
              <blockquote className="text-2xl md:text-3xl font-light text-white leading-relaxed mb-8">
                "Our character is not defined by what we say, but by what we do consistently, 
                day after day, in service of our clients and partners."
              </blockquote>
              <div className="w-16 h-px bg-slate-600 mx-auto mb-6"></div>
              <p className="text-slate-400 font-light">KleverCo Leadership</p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
