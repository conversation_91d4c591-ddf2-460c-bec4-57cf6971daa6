import { Navbar } from "@/components/navigation/navbar";
import { CharacterHero } from "@/components/sections/about/character-hero";
import { CoreValues } from "@/components/sections/about/core-values";
import { CulturePrinciples } from "@/components/sections/about/culture-principles";
import { Footer } from "@/components/navigation/footer";

export const metadata = {
  title: "Character - KleverCo Global Investment & Research Boutique",
  description: "The character, values, and principles that define KleverCo's approach to business, investment, and global operations.",
};

export default function CharacterPage() {
  return (
    <main className="min-h-screen">
      <Navbar />
      <CharacterHero />
      <CoreValues />
      <CulturePrinciples />
      <Footer />
    </main>
  );
}
