"use client";

import { motion } from "framer-motion";
import { MapPin, Phone, Clock, Building } from "lucide-react";

const offices = [
  {
    name: "KLEVER Global",
    country: "United States",
    type: "Global Headquarters",
    address: "30N Gould Street, ste R",
    city: "Sheridan, WY 82801, USA",
    phone: "+****************",
    email: "<EMAIL>",
    hours: "Mon-Fri: 9:00 AM - 6:00 PM (MST)",
    color: "from-blue-500 to-indigo-500",
    services: ["Investment Research", "Strategic Advisory", "Global Operations"]
  },
  {
    name: "KLEVER SDN. BHD.",
    country: "Malaysia", 
    type: "Regional Operations",
    address: "21-9 Office Suites, 1 Mont' Kiara",
    city: "Mont Kiara, 50480 Kuala Lumpur, Malaysia",
    phone: "+60 3 6201 5195",
    email: "<EMAIL>",
    hours: "Mon-Fri: 9:00 AM - 6:00 PM (MYT)",
    color: "from-green-500 to-emerald-500",
    services: ["Business Development", "Regional Advisory", "Market Research"]
  },
  {
    name: "KLEVER Thai Hua Gloves",
    country: "Thailand",
    type: "Manufacturing Hub",
    address: "238/10 Ratchada-Pisek Road",
    city: "Huai-Khwang, Bangkok 10310, Thailand",
    phone: "",
    email: "<EMAIL>",
    hours: "Mon-Fri: 8:00 AM - 5:00 PM (ICT)",
    color: "from-red-500 to-pink-500",
    services: ["PPE Manufacturing", "Quality Control", "Distribution"]
  }
];

export function OfficeLocations() {
  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
            Our Global <span className="text-teal-500">Offices</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            With strategic locations across three continents, we're positioned to serve 
            clients worldwide with local expertise and global perspective.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {offices.map((office, index) => (
            <motion.div
              key={office.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-white rounded-3xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 h-full">
                {/* Header */}
                <div className="text-center mb-6">
                  <div className={`w-16 h-16 bg-gradient-to-r ${office.color} rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <Building className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-slate-900 group-hover:text-teal-600 transition-colors duration-300 mb-2">
                    {office.name}
                  </h3>
                  <p className="text-teal-600 font-semibold">{office.type}</p>
                  <p className="text-slate-500 text-lg font-medium">{office.country}</p>
                </div>

                {/* Address */}
                <div className="mb-6">
                  <div className="flex items-start mb-3">
                    <MapPin className="h-5 w-5 text-slate-400 mr-3 mt-1 flex-shrink-0" />
                    <div>
                      <p className="text-slate-700 font-medium">{office.address}</p>
                      <p className="text-slate-600">{office.city}</p>
                    </div>
                  </div>
                  
                  {office.phone && (
                    <div className="flex items-center mb-3">
                      <Phone className="h-5 w-5 text-slate-400 mr-3" />
                      <a href={`tel:${office.phone}`} className="text-slate-600 hover:text-teal-600 transition-colors duration-300">
                        {office.phone}
                      </a>
                    </div>
                  )}

                  <div className="flex items-center">
                    <Clock className="h-5 w-5 text-slate-400 mr-3" />
                    <p className="text-slate-600 text-sm">{office.hours}</p>
                  </div>
                </div>

                {/* Services */}
                <div className="mb-6">
                  <h4 className="text-lg font-bold text-slate-900 mb-3">Services</h4>
                  <div className="space-y-2">
                    {office.services.map((service, idx) => (
                      <div key={idx} className="flex items-center">
                        <div className={`w-2 h-2 bg-gradient-to-r ${office.color} rounded-full mr-3`}></div>
                        <span className="text-slate-700 text-sm">{service}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* CTA */}
                <div className="space-y-3">
                  <button className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 bg-gradient-to-r ${office.color} text-white hover:shadow-lg hover:scale-105`}>
                    Contact Office
                  </button>
                  <a 
                    href={`mailto:${office.email}`}
                    className="block w-full py-3 rounded-lg font-semibold transition-all duration-300 border-2 border-slate-200 text-slate-700 hover:border-teal-500 hover:text-teal-600 text-center"
                  >
                    Send Email
                  </a>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-20"
        >
          <div className="bg-gradient-to-r from-teal-600 to-blue-600 rounded-3xl p-12 text-white text-center">
            <h3 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Connect?
            </h3>
            <p className="text-xl text-teal-100 mb-8 max-w-2xl mx-auto">
              Whether you're interested in investment opportunities, business development, 
              or our manufacturing capabilities, we're here to help.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a 
                href="mailto:<EMAIL>"
                className="bg-white text-teal-600 hover:bg-teal-50 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300 shadow-lg"
              >
                <EMAIL>
              </a>
              <button className="border-2 border-white text-white hover:bg-white hover:text-teal-600 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300">
                Schedule Meeting
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
