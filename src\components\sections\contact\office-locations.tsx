"use client";

import { motion } from "framer-motion";
import { MapPin, Phone, Clock, Building } from "lucide-react";

const offices = [
  {
    name: "KLEVER Global",
    country: "United States",
    type: "Global Headquarters",
    address: "30N Gould Street, ste R, Sheridan, WY 82801, USA",
    phone: "+****************",
    email: "<EMAIL>"
  },
  {
    name: "KLEVER SDN. BHD.",
    country: "Malaysia",
    type: "Regional Operations",
    address: "21-9 Office Suites, 1 Mont' Kiara, No 1 Jalan Kiara, Mont Kiara, 50480 Kuala Lumpur, Malaysia.",
    phone: "+60 3 6201 5195",
    email: "<EMAIL>"
  },
  {
    name: "KLEVER Thai Hua Gloves",
    country: "Thailand",
    type: "Manufacturing Hub",
    address: "238/10 Ratchada-Pisek Road, Huai-Khwang, Bangkok 10310, Thailand.",
    phone: "",
    email: "<EMAIL>"
  }
];

export function OfficeLocations() {
  return (
    <section className="py-32 bg-white">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-8 tracking-tight">
            Our Global <span className="font-normal text-slate-600">Offices</span>
          </h2>
          <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto mb-8"></div>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed font-light">
            Strategic locations across three continents, positioned to serve clients worldwide
            with local expertise and global perspective.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {offices.map((office, index) => (
            <motion.div
              key={office.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-slate-50 border border-slate-100 rounded-2xl p-8 hover:shadow-lg hover:border-slate-200 transition-all duration-300 h-full">
                {/* Header */}
                <div className="mb-8">
                  <div className="w-14 h-14 bg-slate-900 rounded-xl flex items-center justify-center mb-6 group-hover:bg-slate-800 transition-colors duration-300">
                    <Building className="h-7 w-7 text-white" />
                  </div>
                  <h3 className="text-2xl font-semibold text-slate-900 mb-2 group-hover:text-slate-800 transition-colors duration-300">
                    {office.name}
                  </h3>
                  <p className="text-slate-600 font-medium">{office.type}</p>
                  <p className="text-slate-500 font-light">{office.country}</p>
                </div>

                {/* Contact Information */}
                <div className="space-y-4 mb-8">
                  <div className="flex items-start">
                    <MapPin className="h-5 w-5 text-slate-400 mr-3 mt-1 flex-shrink-0" />
                    <p className="text-slate-700 font-light leading-relaxed">{office.address}</p>
                  </div>

                  {office.phone && (
                    <div className="flex items-center">
                      <Phone className="h-5 w-5 text-slate-400 mr-3" />
                      <a href={`tel:${office.phone}`} className="text-slate-700 hover:text-slate-900 transition-colors duration-300 font-light">
                        {office.phone}
                      </a>
                    </div>
                  )}
                </div>

                {/* Email CTA */}
                <div>
                  <a
                    href={`mailto:${office.email}`}
                    className="block w-full py-3 px-4 rounded-xl font-medium transition-all duration-300 bg-slate-900 text-white hover:bg-slate-800 text-center"
                  >
                    Contact Office
                  </a>
                </div>
              </div>
            </motion.div>
          ))}
        </div>


      </div>
    </section>
  );
}
