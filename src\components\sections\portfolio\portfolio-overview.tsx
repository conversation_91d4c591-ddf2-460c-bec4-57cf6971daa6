"use client";

import { motion } from "framer-motion";
import { Heart, Cpu, Zap, Leaf, Building, Rocket } from "lucide-react";

const sectors = [
  {
    name: "Healthcare",
    icon: Heart,
    companies: 50,
    totalValue: "$3.2B",
    description: "Revolutionary medical technologies and pharmaceutical innovations",
    color: "from-red-500 to-pink-500",
    bgColor: "bg-red-50",
    highlights: ["Biotech breakthroughs", "Medical devices", "Digital health"]
  },
  {
    name: "Technology",
    icon: Cpu,
    companies: 75,
    totalValue: "$4.8B",
    description: "Next-generation software, AI, and digital transformation solutions",
    color: "from-blue-500 to-indigo-500",
    bgColor: "bg-blue-50",
    highlights: ["AI & Machine Learning", "Cloud platforms", "Cybersecurity"]
  },
  {
    name: "Energy",
    icon: Zap,
    companies: 40,
    totalValue: "$2.1B",
    description: "Sustainable energy solutions and clean technology innovations",
    color: "from-green-500 to-emerald-500",
    bgColor: "bg-green-50",
    highlights: ["Renewable energy", "Energy storage", "Smart grid tech"]
  },
  {
    name: "Agriculture",
    icon: Leaf,
    companies: 35,
    totalValue: "$1.8B",
    description: "Advanced farming technologies and sustainable food production",
    color: "from-yellow-500 to-orange-500",
    bgColor: "bg-yellow-50",
    highlights: ["Precision farming", "Food tech", "Sustainable practices"]
  },
  {
    name: "Real Estate",
    icon: Building,
    companies: 25,
    totalValue: "$1.2B",
    description: "Commercial and residential property development projects",
    color: "from-purple-500 to-violet-500",
    bgColor: "bg-purple-50",
    highlights: ["Smart buildings", "Urban development", "PropTech"]
  },
  {
    name: "Aerospace",
    icon: Rocket,
    companies: 10,
    totalValue: "$800M",
    description: "Space technology and advanced aerospace engineering solutions",
    color: "from-indigo-500 to-blue-500",
    bgColor: "bg-indigo-50",
    highlights: ["Satellite tech", "Space exploration", "Defense systems"]
  }
];

export function PortfolioOverview() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
            Portfolio <span className="text-teal-500">Overview</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Our diversified investment portfolio spans six key sectors, each carefully selected 
            for their growth potential and transformative impact on global markets.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {sectors.map((sector, index) => {
            const Icon = sector.icon;
            
            return (
              <motion.div
                key={sector.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="bg-white rounded-3xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 h-full">
                  {/* Header */}
                  <div className="flex items-center mb-6">
                    <div className={`w-16 h-16 bg-gradient-to-r ${sector.color} rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-slate-900 group-hover:text-teal-600 transition-colors duration-300">
                        {sector.name}
                      </h3>
                      <p className="text-slate-500 text-sm">{sector.companies} Companies</p>
                    </div>
                  </div>

                  {/* Value */}
                  <div className="mb-6">
                    <div className="text-3xl font-bold text-slate-900 mb-2">{sector.totalValue}</div>
                    <p className="text-slate-600 leading-relaxed">{sector.description}</p>
                  </div>

                  {/* Highlights */}
                  <div className="space-y-2 mb-6">
                    {sector.highlights.map((highlight, idx) => (
                      <div key={idx} className="flex items-center">
                        <div className={`w-2 h-2 bg-gradient-to-r ${sector.color} rounded-full mr-3`}></div>
                        <span className="text-slate-600 text-sm">{highlight}</span>
                      </div>
                    ))}
                  </div>

                  {/* Progress Bar */}
                  <div className="mb-4">
                    <div className="flex justify-between text-sm text-slate-500 mb-2">
                      <span>Portfolio Allocation</span>
                      <span>{Math.round((sector.companies / 235) * 100)}%</span>
                    </div>
                    <div className="w-full bg-slate-200 rounded-full h-2">
                      <motion.div
                        initial={{ width: 0 }}
                        whileInView={{ width: `${(sector.companies / 235) * 100}%` }}
                        transition={{ duration: 1, delay: index * 0.1 }}
                        viewport={{ once: true }}
                        className={`h-2 bg-gradient-to-r ${sector.color} rounded-full`}
                      />
                    </div>
                  </div>

                  {/* CTA */}
                  <button className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 bg-gradient-to-r ${sector.color} text-white hover:shadow-lg hover:scale-105`}>
                    View {sector.name} Portfolio
                  </button>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Bottom Summary */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-20"
        >
          <div className="bg-gradient-to-r from-slate-50 to-blue-50 rounded-3xl p-12 border border-slate-100">
            <div className="text-center mb-8">
              <h3 className="text-3xl font-bold text-slate-900 mb-4">
                Diversified Excellence
              </h3>
              <p className="text-xl text-slate-700 max-w-2xl mx-auto">
                Our strategic diversification across multiple high-growth sectors ensures 
                balanced risk management and optimized returns for our investors.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div>
                <div className="text-4xl font-bold text-teal-600 mb-2">6</div>
                <div className="text-slate-700">Key Sectors</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-teal-600 mb-2">235+</div>
                <div className="text-slate-700">Total Companies</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-teal-600 mb-2">$12.5B</div>
                <div className="text-slate-700">Total Portfolio Value</div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
