"use client";

import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight, Building, Palette, Waves, Home, Coins, Sparkles } from "lucide-react";

export function RealEstateCompanies() {
  const [currentSlide, setCurrentSlide] = useState(0);

  const companies = [
    {
      name: "TerraTier",
      subtitle: "Real Estate Tokenization",
      description: "TerraTier revolutionizes real estate investment through blockchain tokenization, making property investment more accessible and liquid.",
      details: "Our innovative platform allows fractional ownership of premium real estate assets through secure tokenization technology.",
      icon: Coins,
      color: "blue",
      features: [
        "Blockchain-based tokenization",
        "Fractional ownership opportunities",
        "Enhanced liquidity for investors",
        "Secure digital asset management",
        "Global property access"
      ]
    },
    {
      name: "Vzory Design Studio",
      subtitle: "Architectural Excellence",
      description: "Vzory Design Studio creates exceptional architectural designs that blend innovation with functionality for modern living spaces.",
      details: "Our team of expert architects and designers craft unique spaces that reflect contemporary aesthetics while maintaining practical functionality.",
      icon: Palette,
      color: "purple",
      features: [
        "Contemporary architectural design",
        "Innovative space planning",
        "Sustainable design solutions",
        "Custom residential projects",
        "Commercial space optimization"
      ]
    },
    {
      name: "The10 Ocean Edition",
      subtitle: "Luxury Waterfront Living",
      description: "The10 Ocean Edition offers exclusive waterfront properties with unparalleled ocean views and luxury amenities.",
      details: "Experience the pinnacle of coastal living with our carefully curated collection of premium oceanfront residences.",
      icon: Waves,
      color: "cyan",
      features: [
        "Exclusive oceanfront properties",
        "Luxury amenities and services",
        "Prime waterfront locations",
        "High-end residential developments",
        "Concierge lifestyle services"
      ]
    },
    {
      name: "Pinnacle Developers",
      subtitle: "Premium Development",
      description: "Pinnacle Developers specializes in creating premium residential and commercial developments that set new standards in quality and design.",
      details: "We develop exceptional properties that combine architectural excellence with strategic location advantages for maximum value creation.",
      icon: Building,
      color: "emerald",
      features: [
        "Premium development projects",
        "Strategic location selection",
        "Quality construction standards",
        "Innovative design integration",
        "Value-driven development"
      ]
    }
  ];

  // Auto-advance slideshow for project images
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % companies.length);
    }, 5000);
    return () => clearInterval(timer);
  }, [companies.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % companies.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + companies.length) % companies.length);
  };

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: "bg-blue-500 text-blue-500 from-blue-100 to-blue-200",
      purple: "bg-purple-500 text-purple-500 from-purple-100 to-purple-200", 
      cyan: "bg-cyan-500 text-cyan-500 from-cyan-100 to-cyan-200",
      emerald: "bg-emerald-500 text-emerald-500 from-emerald-100 to-emerald-200"
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <section className="py-32 bg-slate-50">
      <div className="container mx-auto px-6">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-8 tracking-tight">
              Real Estate <span className="font-normal text-slate-600">Portfolio</span>
            </h2>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto mb-8"></div>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed font-light">
              Comprehensive real estate solutions spanning innovation, design, luxury, and development
            </p>
          </motion.div>

          {/* Featured Project Slideshow */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-20"
          >
            <div className="relative bg-white rounded-2xl overflow-hidden shadow-lg">
              <div className="relative aspect-[16/9] bg-gradient-to-br from-slate-200 to-slate-300">
                {/* Placeholder for project images */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <div className="bg-slate-900 p-8 rounded-2xl mb-6 mx-auto w-fit">
                      {(() => {
                        const Icon = companies[currentSlide].icon;
                        return <Icon className="h-16 w-16 text-white" />;
                      })()}
                    </div>
                    <h3 className="text-2xl font-medium text-slate-900 mb-2">
                      {companies[currentSlide].name}
                    </h3>
                    <p className="text-slate-600 font-light">
                      {companies[currentSlide].subtitle}
                    </p>
                  </div>
                </div>

                {/* Navigation Arrows */}
                <button
                  onClick={prevSlide}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white p-3 rounded-full shadow-lg transition-all duration-300"
                >
                  <ChevronLeft className="h-6 w-6 text-slate-700" />
                </button>
                <button
                  onClick={nextSlide}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white p-3 rounded-full shadow-lg transition-all duration-300"
                >
                  <ChevronRight className="h-6 w-6 text-slate-700" />
                </button>

                {/* Slide Indicators */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  {companies.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentSlide(index)}
                      className={`w-3 h-3 rounded-full transition-all duration-300 ${
                        index === currentSlide ? "bg-white" : "bg-white/50"
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>
          </motion.div>

          {/* Companies Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            {companies.map((company, index) => {
              const Icon = company.icon;
              const colorClasses = getColorClasses(company.color);
              const [bgColor, textColor, gradientColors] = colorClasses.split(' ');
              
              return (
                <motion.div
                  key={company.name}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-2xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300"
                >
                  <div className="mb-6">
                    <div className="flex items-center mb-4">
                      <div className={`${bgColor} p-3 rounded-xl mr-4`}>
                        <Icon className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-2xl font-medium text-slate-900">
                          {company.name}
                        </h3>
                        <p className="text-slate-600 font-light">
                          {company.subtitle}
                        </p>
                      </div>
                    </div>
                    <div className="w-16 h-px bg-gradient-to-r from-slate-300 to-transparent mb-6"></div>
                  </div>

                  <div className="space-y-4 mb-6">
                    <p className="text-slate-700 leading-relaxed font-light">
                      {company.description}
                    </p>
                    <p className="text-slate-700 leading-relaxed font-light">
                      {company.details}
                    </p>
                  </div>

                  {/* Features */}
                  <div>
                    <h4 className="text-lg font-medium text-slate-900 mb-4">Key Features</h4>
                    <div className="space-y-2">
                      {company.features.map((feature, idx) => (
                        <div key={idx} className="flex items-center">
                          <div className={`w-2 h-2 ${bgColor} rounded-full mr-3`}></div>
                          <span className="text-slate-600 font-light text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
