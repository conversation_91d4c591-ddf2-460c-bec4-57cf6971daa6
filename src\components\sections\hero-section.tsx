"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { ChevronLeft, ChevronRight } from "lucide-react";

const heroSlides = [
  {
    id: 1,
    title: "Global Investment & Research",
    subtitle: "Boutique Firm",
    description: "A global investment and research boutique firm delivering comprehensive business solutions across international markets.",
    gradient: "from-slate-900 via-blue-900 to-slate-800",
    pattern: "from-blue-500/10 to-purple-500/10"
  },
  {
    id: 2,
    title: "Healthcare Excellence",
    subtitle: "PPE Manufacturing",
    description: "State-of-the-art PPE nitrile glove manufacturers with production factories across USA, Malaysia, and Thailand.",
    gradient: "from-slate-900 via-emerald-900 to-slate-800",
    pattern: "from-emerald-500/10 to-teal-500/10"
  },
  {
    id: 3,
    title: "Technology Innovation",
    subtitle: "IT Solutions",
    description: "Cutting-edge technology solutions through Oratier Technologies and NGT, driving digital transformation.",
    gradient: "from-slate-900 via-purple-900 to-slate-800",
    pattern: "from-purple-500/10 to-indigo-500/10"
  },
  {
    id: 4,
    title: "Energy Solutions",
    subtitle: "Sustainable Future",
    description: "Comprehensive energy solutions spanning oil & gas operations and renewable energy initiatives with TerraTier.",
    gradient: "from-slate-900 via-orange-900 to-slate-800",
    pattern: "from-orange-500/10 to-red-500/10"
  },
  {
    id: 5,
    title: "Aviation Excellence",
    subtitle: "Pinnacle Copters",
    description: "Premium helicopter services and aviation solutions delivering exceptional aerial transportation and logistics.",
    gradient: "from-slate-900 via-cyan-900 to-slate-800",
    pattern: "from-cyan-500/10 to-blue-500/10"
  },
  {
    id: 6,
    title: "Food & Beverage",
    subtitle: "The Orange Co",
    description: "Quality food and beverage solutions with focus on premium products and sustainable practices.",
    gradient: "from-slate-900 via-amber-900 to-slate-800",
    pattern: "from-amber-500/10 to-orange-500/10"
  }
];

export function HeroSection() {
  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }, 5000);

    return () => clearInterval(timer);
  }, []);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length);
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Animated Background Slides */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentSlide}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 1 }}
          className={`absolute inset-0 bg-gradient-to-br ${heroSlides[currentSlide].gradient}`}
        />
      </AnimatePresence>

      {/* Dynamic Pattern Overlay */}
      <AnimatePresence mode="wait">
        <motion.div
          key={`pattern-${currentSlide}`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 1 }}
          className={`absolute inset-0 bg-gradient-to-br ${heroSlides[currentSlide].pattern}`}
        />
      </AnimatePresence>

      {/* Glassmorphism overlay */}
      <div className="absolute inset-0 bg-black/20 backdrop-blur-[1px]" />

      {/* Subtle geometric pattern */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%22100%22%20height%3D%22100%22%20viewBox%3D%220%200%20100%20100%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.02%22%3E%3Cpath%20d%3D%22M50%2050L0%200h100L50%2050zM50%2050L100%20100H0L50%2050z%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="text-center max-w-5xl mx-auto">
          {/* Company Logo */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-12"
          >
            <div className="flex justify-center mb-6">
              <Image
                src="/images/logo-klever.png"
                alt="KleverCo Logo"
                width={240}
                height={96}
                className="h-24 w-auto"
              />
            </div>
            <div className="h-1 w-32 bg-gradient-to-r from-white/60 via-white to-white/60 mx-auto rounded-full"></div>
          </motion.div>

          {/* Dynamic Content */}
          <AnimatePresence mode="wait">
            <motion.div
              key={currentSlide}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -30 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h1 className="text-3xl md:text-5xl lg:text-6xl font-light text-white mb-4 leading-tight tracking-tight">
                {heroSlides[currentSlide].title}
              </h1>
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-semibold text-white/90 mb-8 tracking-tight">
                {heroSlides[currentSlide].subtitle}
              </h2>
              <p className="text-lg md:text-xl text-white/80 max-w-3xl mx-auto leading-relaxed font-light">
                {heroSlides[currentSlide].description}
              </p>
            </motion.div>
          </AnimatePresence>

          {/* Navigation Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex justify-center items-center gap-6 mb-12"
          >
            <Link href="/businesses/healthcare">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-8 py-4 bg-white/10 backdrop-blur-md border border-white/20 text-white rounded-xl font-medium hover:bg-white/20 transition-all duration-300"
              >
                Explore Our Businesses
              </motion.button>
            </Link>
            <Link href="/about">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-8 py-4 bg-white text-slate-900 rounded-xl font-medium hover:bg-white/90 transition-all duration-300"
              >
                Learn About Us
              </motion.button>
            </Link>
          </motion.div>

          {/* Slide Indicators */}
          <div className="flex justify-center space-x-3 mb-8">
            {heroSlides.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentSlide
                    ? "bg-white scale-125"
                    : "bg-white/40 hover:bg-white/60"
                }`}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Navigation Arrows */}
      <button
        onClick={prevSlide}
        className="absolute left-6 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/10 backdrop-blur-md border border-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300 z-20"
      >
        <ChevronLeft className="h-6 w-6" />
      </button>

      <button
        onClick={nextSlide}
        className="absolute right-6 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/10 backdrop-blur-md border border-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300 z-20"
      >
        <ChevronRight className="h-6 w-6" />
      </button>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="w-6 h-10 border-2 border-white/60 rounded-full flex justify-center"
        >
          <motion.div
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-1 h-3 bg-white/60 rounded-full mt-2"
          />
        </motion.div>
      </motion.div>
    </section>
  );
}
