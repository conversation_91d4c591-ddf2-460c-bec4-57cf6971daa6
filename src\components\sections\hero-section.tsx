"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { ChevronLeft, ChevronRight } from "lucide-react";

const heroSlides = [
  {
    id: 1,
    title: "Global Investment & Research",
    subtitle: "Boutique Firm",
    description: "A global investment and research boutique firm delivering comprehensive business solutions across international markets.",
    gradient: "from-slate-900 via-blue-900 to-slate-800",
    pattern: "from-blue-500/20 to-purple-500/20",
    backgroundImage: "url('https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')"
  },
  {
    id: 2,
    title: "Healthcare Excellence",
    subtitle: "PPE Manufacturing",
    description: "State-of-the-art PPE nitrile glove manufacturers with production factories across USA, Malaysia, and Thailand.",
    gradient: "from-slate-900 via-emerald-900 to-slate-800",
    pattern: "from-emerald-500/20 to-teal-500/20",
    backgroundImage: "url('https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')"
  },
  {
    id: 3,
    title: "Technology Innovation",
    subtitle: "IT Solutions",
    description: "Cutting-edge technology solutions through Oratier Technologies and NGT, driving digital transformation.",
    gradient: "from-slate-900 via-purple-900 to-slate-800",
    pattern: "from-purple-500/20 to-indigo-500/20",
    backgroundImage: "url('https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=2125&q=80')"
  },
  {
    id: 4,
    title: "Energy Solutions",
    subtitle: "Sustainable Future",
    description: "Comprehensive energy solutions spanning oil & gas operations and renewable energy initiatives with TerraTier.",
    gradient: "from-slate-900 via-orange-900 to-slate-800",
    pattern: "from-orange-500/20 to-red-500/20",
    backgroundImage: "url('https://images.unsplash.com/photo-1466611653911-95081537e5b7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')"
  },
  {
    id: 5,
    title: "Aviation Excellence",
    subtitle: "Pinnacle Copters",
    description: "Premium helicopter services and aviation solutions delivering exceptional aerial transportation and logistics.",
    gradient: "from-slate-900 via-cyan-900 to-slate-800",
    pattern: "from-cyan-500/20 to-blue-500/20",
    backgroundImage: "url('https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')"
  }
];

export function HeroSection() {
  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }, 5000);

    return () => clearInterval(timer);
  }, []);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length);
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image with Parallax Effect */}
      <AnimatePresence mode="wait">
        <motion.div
          key={`bg-${currentSlide}`}
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 1.5, ease: "easeInOut" }}
          className="absolute inset-0"
          style={{
            backgroundImage: heroSlides[currentSlide].backgroundImage,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat'
          }}
        />
      </AnimatePresence>

      {/* Luxury Gradient Overlay */}
      <AnimatePresence mode="wait">
        <motion.div
          key={`gradient-${currentSlide}`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 1.2 }}
          className={`absolute inset-0 bg-gradient-to-br ${heroSlides[currentSlide].gradient} opacity-85`}
        />
      </AnimatePresence>

      {/* Premium Pattern Overlay */}
      <AnimatePresence mode="wait">
        <motion.div
          key={`pattern-${currentSlide}`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 1.5 }}
          className={`absolute inset-0 bg-gradient-to-br ${heroSlides[currentSlide].pattern}`}
        />
      </AnimatePresence>

      {/* Sophisticated Glassmorphism overlay */}
      <div className="absolute inset-0 bg-black/30 backdrop-blur-[2px]" />

      {/* Luxury geometric pattern */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%22120%22%20height%3D%22120%22%20viewBox%3D%220%200%20120%20120%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Cpath%20d%3D%22M60%2060L0%200h120L60%2060zM60%2060L120%20120H0L60%2060z%22/%3E%3Cpath%20d%3D%22M60%2060L30%2030h60L60%2060zM60%2060L90%2090H30L60%2060z%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>

      {/* Premium border glow effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>

      <div className="container mx-auto px-6 relative z-20">
        <div className="text-center max-w-6xl mx-auto">
          {/* Premium Company Logo */}
          <motion.div
            initial={{ opacity: 0, y: -30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
            className="mb-16"
          >
            <div className="flex justify-center mb-8">
              <div className="relative">
                <Image
                  src="/images/logo-klever.png"
                  alt="KleverCo Logo"
                  width={320}
                  height={128}
                  className="h-32 w-auto filter drop-shadow-2xl"
                />
                {/* Premium logo glow */}
                <div className="absolute inset-0 bg-white/20 blur-3xl rounded-full scale-150 opacity-50"></div>
              </div>
            </div>
            <div className="h-1 w-40 bg-gradient-to-r from-transparent via-white/80 to-transparent mx-auto rounded-full"></div>
            <div className="h-px w-24 bg-gradient-to-r from-transparent via-white/60 to-transparent mx-auto rounded-full mt-2"></div>
          </motion.div>

          {/* Luxury Dynamic Content */}
          <AnimatePresence mode="wait">
            <motion.div
              key={currentSlide}
              initial={{ opacity: 0, y: 50, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -50, scale: 1.05 }}
              transition={{ duration: 1, ease: "easeInOut" }}
              className="mb-16"
            >
              <div className="bg-white/5 backdrop-blur-md rounded-3xl p-12 border border-white/10 shadow-2xl">
                <h1 className="text-4xl md:text-6xl lg:text-7xl font-light text-white mb-6 leading-tight tracking-tight">
                  {heroSlides[currentSlide].title}
                </h1>
                <div className="w-20 h-1 bg-gradient-to-r from-white/60 to-white/30 mx-auto rounded-full mb-8"></div>
                <h2 className="text-2xl md:text-4xl lg:text-5xl font-medium text-white/95 mb-10 tracking-tight">
                  {heroSlides[currentSlide].subtitle}
                </h2>
                <p className="text-xl md:text-2xl text-white/85 max-w-4xl mx-auto leading-relaxed font-light">
                  {heroSlides[currentSlide].description}
                </p>
              </div>
            </motion.div>
          </AnimatePresence>

          {/* Premium Navigation Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.8 }}
            className="flex flex-col sm:flex-row justify-center items-center gap-6 mb-16"
          >
            <Link href="/businesses/healthcare">
              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="px-10 py-5 bg-white/15 backdrop-blur-xl border-2 border-white/30 text-white rounded-2xl font-semibold text-lg hover:bg-white/25 hover:border-white/50 transition-all duration-500 shadow-2xl"
              >
                Explore Our Businesses
              </motion.button>
            </Link>
            <Link href="/about">
              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="px-10 py-5 bg-white text-slate-900 rounded-2xl font-semibold text-lg hover:bg-white/95 hover:shadow-2xl transition-all duration-500 shadow-xl"
              >
                Learn About Us
              </motion.button>
            </Link>
          </motion.div>

          {/* Luxury Slide Indicators */}
          <div className="flex justify-center space-x-4 mb-12">
            {heroSlides.map((_, index) => (
              <motion.button
                key={index}
                whileHover={{ scale: 1.2 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setCurrentSlide(index)}
                className={`relative transition-all duration-500 ${
                  index === currentSlide
                    ? "w-12 h-4 bg-white rounded-full shadow-lg"
                    : "w-4 h-4 bg-white/50 hover:bg-white/70 rounded-full"
                }`}
              >
                {index === currentSlide && (
                  <div className="absolute inset-0 bg-white/30 rounded-full blur-md scale-150"></div>
                )}
              </motion.button>
            ))}
          </div>
        </div>
      </div>

      {/* Premium Navigation Arrows */}
      <motion.button
        whileHover={{ scale: 1.1, x: -5 }}
        whileTap={{ scale: 0.9 }}
        onClick={prevSlide}
        className="absolute left-8 top-1/2 transform -translate-y-1/2 w-16 h-16 bg-white/15 backdrop-blur-xl border-2 border-white/30 rounded-2xl flex items-center justify-center text-white hover:bg-white/25 hover:border-white/50 transition-all duration-500 z-30 shadow-2xl"
      >
        <ChevronLeft className="h-8 w-8" />
      </motion.button>

      <motion.button
        whileHover={{ scale: 1.1, x: 5 }}
        whileTap={{ scale: 0.9 }}
        onClick={nextSlide}
        className="absolute right-8 top-1/2 transform -translate-y-1/2 w-16 h-16 bg-white/15 backdrop-blur-xl border-2 border-white/30 rounded-2xl flex items-center justify-center text-white hover:bg-white/25 hover:border-white/50 transition-all duration-500 z-30 shadow-2xl"
      >
        <ChevronRight className="h-8 w-8" />
      </motion.button>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="w-6 h-10 border-2 border-white/60 rounded-full flex justify-center"
        >
          <motion.div
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-1 h-3 bg-white/60 rounded-full mt-2"
          />
        </motion.div>
      </motion.div>
    </section>
  );
}
