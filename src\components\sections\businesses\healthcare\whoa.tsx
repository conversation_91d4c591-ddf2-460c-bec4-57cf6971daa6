"use client";

import { motion } from "framer-motion";
import { Smartphone, Users, Shield, Database, Globe, Heart } from "lucide-react";

export function WHOA() {
  const features = [
    {
      icon: Users,
      title: "User-Friendly Interface",
      description: "Easily operated by any age group with intuitive design and simple navigation"
    },
    {
      icon: Shield,
      title: "Comprehensive Modules",
      description: "Diagnostics to electronic medical records with complete healthcare coverage"
    },
    {
      icon: Database,
      title: "Supply Chain Management",
      description: "Complete supply chain solutions with regulatory compliance integration"
    },
    {
      icon: Globe,
      title: "Global Compliance",
      description: "Compliance with various government health and food regulators worldwide"
    },
    {
      icon: Heart,
      title: "HL7 Standards",
      description: "Built upon Health Level 7 standards for seamless healthcare data exchange"
    },
    {
      icon: Smartphone,
      title: "Mobile Accessibility",
      description: "Full mobile application with cross-platform compatibility and real-time updates"
    }
  ];

  return (
    <section className="py-32 bg-white">
      <div className="container mx-auto px-6">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-4 tracking-tight">
              WHOA
            </h2>
            <p className="text-2xl text-slate-600 mb-8 font-light">
              World Health - One Application
            </p>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto mb-8"></div>
            <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed font-light">
              A comprehensive healthcare application born from our mission to contribute during the COVID-19 pandemic
            </p>
          </motion.div>

          {/* Main Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-20"
          >
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              {/* Content */}
              <div>
                <div className="bg-slate-50 rounded-2xl p-8 border border-slate-100 mb-8">
                  <h3 className="text-2xl font-medium text-slate-900 mb-6">
                    Our Mission
                  </h3>
                  <div className="space-y-4">
                    <p className="text-lg text-slate-700 leading-relaxed font-light">
                      Long standing experience of over two decades in digitalizing and automating healthcare sector globally. At the advent of this COVID-19 pandemic it made us feel responsible to contribute our part in alleviating the chaos.
                    </p>
                    <p className="text-slate-600 leading-relaxed font-light">
                      To address this pandemic, we came upon the mission to bring a user-friendly app which could easily be operated and run by any age group.
                    </p>
                  </div>
                </div>

                <div className="bg-slate-900 rounded-2xl p-8 text-white">
                  <h3 className="text-2xl font-light mb-6 tracking-tight">
                    Application Capabilities
                  </h3>
                  <div className="w-16 h-px bg-slate-400 mb-6"></div>
                  <p className="text-slate-300 leading-relaxed font-light text-lg">
                    World Health One App "WHOA" identifies, guides, and facilitates interaction of people amongst each other. It has several modules addressing diagnostics to electronic medical record to supply chain management with compliance to various governments health and food regulators upon HL7 standards.
                  </p>
                </div>
              </div>

              {/* Features Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                {features.map((feature, index) => {
                  const Icon = feature.icon;
                  
                  return (
                    <motion.div
                      key={feature.title}
                      initial={{ opacity: 0, y: 30 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="bg-white rounded-xl p-6 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300"
                    >
                      <div className="bg-slate-900 p-3 rounded-xl w-fit mb-4">
                        <Icon className="h-6 w-6 text-white" />
                      </div>
                      <h4 className="text-lg font-medium text-slate-900 mb-3">
                        {feature.title}
                      </h4>
                      <p className="text-slate-600 font-light text-sm leading-relaxed">
                        {feature.description}
                      </p>
                    </motion.div>
                  );
                })}
              </div>
            </div>
          </motion.div>

          {/* Key Benefits */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <div className="text-center mb-16">
              <h3 className="text-3xl font-light text-slate-900 mb-8 tracking-tight">
                Key <span className="font-normal text-slate-600">Benefits</span>
              </h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  title: "Pandemic Response",
                  description: "Specifically designed to address pandemic challenges with real-time monitoring and response capabilities",
                  highlight: "COVID-19 Focus"
                },
                {
                  title: "Universal Accessibility",
                  description: "User-friendly interface designed for operation by any age group with minimal technical knowledge required",
                  highlight: "All Ages"
                },
                {
                  title: "Comprehensive Integration",
                  description: "Complete healthcare ecosystem from diagnostics to supply chain management in one unified platform",
                  highlight: "End-to-End"
                }
              ].map((benefit, index) => (
                <motion.div
                  key={benefit.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-slate-50 rounded-2xl p-8 border border-slate-100 text-center"
                >
                  <div className="bg-slate-900 text-white px-4 py-2 rounded-full text-sm font-medium mb-6 inline-block">
                    {benefit.highlight}
                  </div>
                  <h4 className="text-xl font-medium text-slate-900 mb-4">
                    {benefit.title}
                  </h4>
                  <p className="text-slate-600 font-light leading-relaxed">
                    {benefit.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
