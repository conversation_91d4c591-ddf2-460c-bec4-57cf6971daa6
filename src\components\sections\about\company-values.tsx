"use client";

import { motion } from "framer-motion";
import { Shield, Users, Zap, Heart, Globe, Award } from "lucide-react";

const values = [
  {
    icon: Shield,
    title: "Trust & Reliability",
    description: "Building lasting relationships through consistent delivery and unwavering commitment to our partners' success.",
    color: "from-blue-500 to-indigo-500"
  },
  {
    icon: Users,
    title: "Collaborative Partnership",
    description: "Working hand-in-hand with entrepreneurs and management teams to achieve shared goals and mutual growth.",
    color: "from-teal-500 to-cyan-500"
  },
  {
    icon: Zap,
    title: "Agile Excellence",
    description: "Adapting quickly to market changes while maintaining the highest standards of investment discipline.",
    color: "from-yellow-500 to-orange-500"
  },
  {
    icon: Heart,
    title: "Passionate Commitment",
    description: "Bringing genuine enthusiasm and dedication to every investment opportunity and partnership we pursue.",
    color: "from-red-500 to-pink-500"
  },
  {
    icon: Globe,
    title: "Global Mindset",
    description: "Leveraging international perspectives and cross-border expertise to unlock worldwide opportunities.",
    color: "from-green-500 to-emerald-500"
  },
  {
    icon: Award,
    title: "Performance Excellence",
    description: "Consistently delivering superior results through rigorous analysis, strategic thinking, and operational excellence.",
    color: "from-purple-500 to-violet-500"
  }
];

export function CompanyValues() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
            Our <span className="text-teal-500">Values</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            The core values that define our culture, guide our decisions, and shape our relationships 
            with partners, portfolio companies, and stakeholders worldwide.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {values.map((value, index) => {
            const Icon = value.icon;
            
            return (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="bg-white rounded-2xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 h-full">
                  <div className={`w-16 h-16 bg-gradient-to-r ${value.color} rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="h-8 w-8 text-white" />
                  </div>
                  
                  <h3 className="text-xl font-bold text-slate-900 mb-4 group-hover:text-teal-600 transition-colors duration-300">
                    {value.title}
                  </h3>
                  
                  <p className="text-slate-600 leading-relaxed">
                    {value.description}
                  </p>
                  
                  {/* Decorative element */}
                  <div className={`w-full h-1 bg-gradient-to-r ${value.color} rounded-full mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300`}></div>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Bottom CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-20"
        >
          <div className="bg-gradient-to-r from-teal-600 to-blue-600 rounded-3xl p-12 text-white">
            <h3 className="text-3xl md:text-4xl font-bold mb-6">
              Experience Our Values in Action
            </h3>
            <p className="text-xl text-teal-100 mb-8 max-w-2xl mx-auto">
              See how our values translate into exceptional results and lasting partnerships 
              across our diverse portfolio of successful investments.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-teal-600 hover:bg-teal-50 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300 shadow-lg">
                View Success Stories
              </button>
              <button className="border-2 border-white text-white hover:bg-white hover:text-teal-600 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300">
                Partner With Us
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
