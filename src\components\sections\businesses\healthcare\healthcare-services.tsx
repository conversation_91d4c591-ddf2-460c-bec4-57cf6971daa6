"use client";

import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight, Video, Database, Globe, Shield, Smartphone } from "lucide-react";

export function HealthcareServices() {
  const [currentSlide, setCurrentSlide] = useState(0);

  const healthcareStats = [
    {
      value: "50+ million",
      label: "Patients treated ANNUALLY over Telemedicine"
    },
    {
      value: "110+ million MT",
      label: "Patients treated ANNUALLY over HIMS"
    },
    {
      value: "30+ Remote Centers",
      label: "Linked to tertiary hospitals"
    },
    {
      value: "50+ Tertiary Hospitals",
      label: "Proving treatments over HIMS"
    },
    {
      value: "250+ million",
      label: "EMR with unlimited scalability"
    }
  ];

  const telemedicineComponents = [
    {
      title: "Communication Technology",
      description: "Video conferencing, audio calls, and secure messaging systems enabling real-time interactions between healthcare providers and patients.",
      icon: Video
    },
    {
      title: "Digital Health Records",
      description: "Electronic health records (EHRs) and digital documentation systems allowing secure access and updates to patient information.",
      icon: Database
    },
    {
      title: "Remote Monitoring",
      description: "Monitoring patients' vital signs using wearable devices and connected medical equipment between virtual visits.",
      icon: Shield
    },
    {
      title: "Mobile Health (mHealth)",
      description: "Mobile applications enabling virtual visits, symptom sharing, and medication reminders through smartphones and tablets.",
      icon: Smartphone
    },
    {
      title: "Store-and-Forward",
      description: "Capturing and storing medical information for later review, particularly useful in dermatology and radiology specialties.",
      icon: Globe
    }
  ];

  // Auto-advance slideshow
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % healthcareStats.length);
    }, 4000);
    return () => clearInterval(timer);
  }, [healthcareStats.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % healthcareStats.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + healthcareStats.length) % healthcareStats.length);
  };

  return (
    <section className="py-32 bg-white">
      <div className="container mx-auto px-6">
        <div className="max-w-7xl mx-auto">
          {/* Telemedicine Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-32"
          >
            <div className="text-center mb-20">
              <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-8 tracking-tight">
                Telemedicine
              </h2>
              <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto mb-8"></div>
              <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed font-light">
                Healthcare services provided remotely using telecommunications technology, enabling healthcare professionals to evaluate, diagnose, and treat patients without in-person visits.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
              {/* Content */}
              <div>
                <div className="space-y-6 mb-12">
                  <p className="text-lg text-slate-700 leading-relaxed font-light">
                    Endemic and pandemic initiation in a demography can be recognized and addressed promptly as the data immediately shows rise in a certain area of suddenly becoming prevalent disease all powered by Telemedicine and statistics assimilation.
                  </p>
                </div>

                {/* Smart Emergency Response System */}
                <div className="bg-slate-50 rounded-2xl p-8 border border-slate-100">
                  <h3 className="text-2xl font-medium text-slate-900 mb-4">
                    Smart Emergency Response System (SERS)
                  </h3>
                  <p className="text-slate-700 leading-relaxed font-light">
                    Emergency situations like trauma demand rapid response. Our SERS transforms emergency response services by eliminating critical time lost in briefing doctors, potentially saving lives through immediate information transfer.
                  </p>
                </div>
              </div>

              {/* Components Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                {telemedicineComponents.map((component, index) => {
                  const Icon = component.icon;
                  
                  return (
                    <motion.div
                      key={component.title}
                      initial={{ opacity: 0, y: 30 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="bg-white rounded-xl p-6 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300"
                    >
                      <div className="bg-slate-900 p-3 rounded-xl w-fit mb-4">
                        <Icon className="h-6 w-6 text-white" />
                      </div>
                      <h4 className="text-lg font-medium text-slate-900 mb-3">
                        {component.title}
                      </h4>
                      <p className="text-slate-600 font-light text-sm leading-relaxed">
                        {component.description}
                      </p>
                    </motion.div>
                  );
                })}
              </div>
            </div>
          </motion.div>

          {/* Healthcare at a Glance - Slideshow */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-32"
          >
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-8 tracking-tight">
                Healthcare at a <span className="font-normal text-slate-600">Glance</span>
              </h2>
              <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto"></div>
            </div>

            <div className="relative bg-slate-50 rounded-2xl overflow-hidden">
              <div className="relative aspect-[16/6] flex items-center justify-center">
                {/* Current Stat Display */}
                <div className="text-center">
                  <div className="text-5xl md:text-6xl font-light text-slate-900 mb-4 tracking-tight">
                    {healthcareStats[currentSlide].value}
                  </div>
                  <div className="text-xl text-slate-600 font-light max-w-md">
                    {healthcareStats[currentSlide].label}
                  </div>
                </div>

                {/* Navigation Arrows */}
                <button
                  onClick={prevSlide}
                  className="absolute left-6 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white p-3 rounded-full shadow-lg transition-all duration-300"
                >
                  <ChevronLeft className="h-6 w-6 text-slate-700" />
                </button>
                <button
                  onClick={nextSlide}
                  className="absolute right-6 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white p-3 rounded-full shadow-lg transition-all duration-300"
                >
                  <ChevronRight className="h-6 w-6 text-slate-700" />
                </button>

                {/* Slide Indicators */}
                <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  {healthcareStats.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentSlide(index)}
                      className={`w-3 h-3 rounded-full transition-all duration-300 ${
                        index === currentSlide ? "bg-slate-900" : "bg-slate-400"
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>
          </motion.div>

          {/* Impact Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <div className="text-center mb-16">
              <h3 className="text-3xl font-light text-slate-900 mb-8 tracking-tight">
                Impact & <span className="font-normal text-slate-600">Benefits</span>
              </h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: "Increased Access",
                  description: "Eliminates geographical barriers, making healthcare accessible to individuals in remote or underserved areas."
                },
                {
                  title: "Convenience",
                  description: "Patients receive medical consultations from home, reducing travel and waiting room time."
                },
                {
                  title: "Cost-Effective",
                  description: "Reduces expenses for both patients and providers, eliminating travel and facility maintenance costs."
                },
                {
                  title: "Timely Consultations",
                  description: "Enables urgent consultations and effective management of chronic conditions."
                },
                {
                  title: "Continuity of Care",
                  description: "Facilitates ongoing communication supporting continuous monitoring and follow-up care."
                }
              ].map((impact, index) => (
                <motion.div
                  key={impact.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-slate-50 rounded-2xl p-8 border border-slate-100"
                >
                  <h4 className="text-xl font-medium text-slate-900 mb-4">
                    {impact.title}
                  </h4>
                  <p className="text-slate-600 font-light leading-relaxed">
                    {impact.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
