"use client";

import { motion } from "framer-motion";
import { Shield, Factory, Award, Globe, CheckCircle, Heart } from "lucide-react";

const manufacturingFeatures = [
  {
    title: "State-of-the-Art Facilities",
    description: "Modern manufacturing facilities equipped with advanced technology for high-quality PPE production.",
    icon: Factory,
    color: "from-blue-500 to-indigo-500"
  },
  {
    title: "Quality Assurance",
    description: "Rigorous quality control processes ensuring all products meet international safety standards.",
    icon: Award,
    color: "from-green-500 to-emerald-500"
  },
  {
    title: "Global Distribution",
    description: "Comprehensive distribution network serving healthcare providers worldwide with reliable supply chains.",
    icon: Globe,
    color: "from-purple-500 to-violet-500"
  },
  {
    title: "Safety Standards",
    description: "Commitment to highest safety standards and regulatory compliance across all manufacturing processes.",
    icon: Shield,
    color: "from-red-500 to-pink-500"
  }
];

const qualityStandards = [
  "ISO 13485 Medical Device Quality Management",
  "FDA Approved Manufacturing Processes",
  "CE Marking Compliance",
  "ASTM International Standards",
  "EN 455 European Standards",
  "ANSI/AAMI Standards"
];

export function ManufacturingCapabilities() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
            Manufacturing <span className="text-teal-500">Capabilities</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            State-of-the-art PPE nitrile glove manufacturing with production facilities 
            ensuring highest quality standards for global healthcare needs.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {manufacturingFeatures.map((feature, index) => {
            const Icon = feature.icon;
            
            return (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="bg-gradient-to-br from-slate-50 to-blue-50 rounded-3xl p-8 border border-slate-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 h-full">
                  <div className="flex items-start mb-6">
                    <div className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-slate-900 group-hover:text-teal-600 transition-colors duration-300 mb-3">
                        {feature.title}
                      </h3>
                      <p className="text-slate-600 leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Manufacturing Hub */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="bg-gradient-to-r from-teal-600 to-blue-600 rounded-3xl p-12 text-white mb-16"
        >
          <div className="text-center mb-8">
            <Factory className="h-16 w-16 text-teal-200 mx-auto mb-6" />
            <h3 className="text-3xl md:text-4xl font-bold mb-6">
              KLEVER Thai Hua Gloves
            </h3>
            <p className="text-xl text-teal-100 max-w-2xl mx-auto">
              Our dedicated manufacturing hub in Bangkok, Thailand, specializing in 
              high-quality nitrile glove production for global healthcare markets.
            </p>
          </div>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
            <div className="text-teal-200 mb-2">Manufacturing Location</div>
            <div className="text-xl font-bold mb-1">238/10 Ratchada-Pisek Road</div>
            <div className="text-teal-200">Huai-Khwang, Bangkok 10310, Thailand</div>
          </div>
        </motion.div>

        {/* Quality Standards */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="bg-white rounded-3xl p-12 shadow-lg border border-slate-100"
        >
          <div className="text-center mb-12">
            <Award className="h-16 w-16 text-teal-500 mx-auto mb-6" />
            <h3 className="text-3xl md:text-4xl font-bold text-slate-900 mb-6">
              Quality & Compliance Standards
            </h3>
            <p className="text-xl text-slate-700 max-w-2xl mx-auto">
              Our manufacturing processes adhere to the highest international quality 
              and safety standards, ensuring reliable protection for healthcare professionals.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {qualityStandards.map((standard, index) => (
              <motion.div
                key={standard}
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-center bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4"
              >
                <CheckCircle className="h-6 w-6 text-green-500 mr-3 flex-shrink-0" />
                <span className="text-slate-700 font-medium">{standard}</span>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-br from-slate-50 to-blue-50 rounded-3xl p-12">
            <Heart className="h-16 w-16 text-red-500 mx-auto mb-6" />
            <h3 className="text-3xl md:text-4xl font-bold text-slate-900 mb-6">
              We Serve Humanity - HEALTH
            </h3>
            <p className="text-xl text-slate-700 mb-8 max-w-2xl mx-auto">
              Our commitment to serving humanity through health drives our dedication 
              to producing the highest quality PPE for global healthcare protection.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-teal-500 hover:bg-teal-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300 shadow-lg">
                Learn About Our Products
              </button>
              <button className="border-2 border-teal-500 text-teal-600 hover:bg-teal-500 hover:text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300">
                Contact Manufacturing Team
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
