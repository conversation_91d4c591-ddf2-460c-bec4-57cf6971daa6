"use client";

import { motion } from "framer-motion";
import { TrendingUp, Building, Users, Heart, ArrowRight } from "lucide-react";

const businessServices = [
  {
    title: "We Develop Business",
    subtitle: "Facilitate Business Growth",
    description: "Comprehensive business development services focused on facilitating sustainable growth through strategic planning, market analysis, and operational optimization.",
    icon: TrendingUp,
    color: "from-blue-500 to-indigo-500",
    features: [
      "Strategic Business Planning",
      "Market Entry Strategies", 
      "Growth Facilitation",
      "Performance Optimization"
    ]
  },
  {
    title: "We Support Business", 
    subtitle: "Business Innovation and Support",
    description: "Innovative business support solutions designed to enhance operational efficiency, drive innovation, and provide comprehensive support for business transformation.",
    icon: Building,
    color: "from-green-500 to-emerald-500",
    features: [
      "Innovation Consulting",
      "Operational Support",
      "Technology Integration",
      "Process Improvement"
    ]
  },
  {
    title: "We Advise",
    subtitle: "Expert Financial Advice", 
    description: "Professional financial advisory services providing expert guidance on investment strategies, financial planning, and strategic decision-making for optimal business outcomes.",
    icon: Users,
    color: "from-purple-500 to-violet-500",
    features: [
      "Investment Advisory",
      "Financial Planning",
      "Risk Assessment",
      "Strategic Consulting"
    ]
  },
  {
    title: "We Serve Humanity",
    subtitle: "HEALTH",
    description: "Dedicated to serving humanity through health-focused initiatives, including state-of-the-art PPE manufacturing and healthcare solutions for global well-being.",
    icon: Heart,
    color: "from-red-500 to-pink-500",
    features: [
      "PPE Manufacturing",
      "Healthcare Solutions",
      "Quality Assurance",
      "Global Distribution"
    ]
  }
];

export function BusinessServices() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
            Our <span className="text-teal-500">Business Services</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Comprehensive solutions across four key business verticals, delivering value through 
            strategic expertise, innovation, and commitment to excellence.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {businessServices.map((service, index) => {
            const Icon = service.icon;
            
            return (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="bg-gradient-to-br from-slate-50 to-blue-50 rounded-3xl p-8 border border-slate-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 h-full">
                  {/* Header */}
                  <div className="flex items-start mb-6">
                    <div className={`w-16 h-16 bg-gradient-to-r ${service.color} rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-slate-900 group-hover:text-teal-600 transition-colors duration-300 mb-1">
                        {service.title}
                      </h3>
                      <p className="text-teal-600 font-semibold text-lg">{service.subtitle}</p>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-slate-600 leading-relaxed mb-6">
                    {service.description}
                  </p>

                  {/* Features */}
                  <div className="space-y-3 mb-6">
                    {service.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center">
                        <div className={`w-2 h-2 bg-gradient-to-r ${service.color} rounded-full mr-3`}></div>
                        <span className="text-slate-700 font-medium">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* CTA */}
                  <button className="group/btn w-full py-3 rounded-lg font-semibold transition-all duration-300 bg-white border-2 border-slate-200 hover:border-teal-500 text-slate-700 hover:text-teal-600 flex items-center justify-center">
                    Learn More
                    <ArrowRight className="h-5 w-5 ml-2 group-hover/btn:translate-x-1 transition-transform duration-300" />
                  </button>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Bottom CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-20"
        >
          <div className="bg-gradient-to-r from-teal-600 to-blue-600 rounded-3xl p-12 text-white text-center">
            <h3 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Partner with KleverCo?
            </h3>
            <p className="text-xl text-teal-100 mb-8 max-w-2xl mx-auto">
              Discover how our comprehensive business services can drive growth, 
              innovation, and success for your organization.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-teal-600 hover:bg-teal-50 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300 shadow-lg">
                Schedule Consultation
              </button>
              <button className="border-2 border-white text-white hover:bg-white hover:text-teal-600 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300">
                View Case Studies
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
