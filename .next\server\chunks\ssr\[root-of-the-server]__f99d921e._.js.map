{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/navigation/navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { Button } from \"@/components/ui/button\";\nimport { TrendingUp, Menu, X, ChevronDown } from \"lucide-react\";\n\nconst navItems = [\n  { name: \"Home\", href: \"/\" },\n  {\n    name: \"About\",\n    href: \"/about\",\n    dropdown: [\n      { name: \"About Us\", href: \"/about\" },\n      { name: \"Members\", href: \"/about/members\" },\n      { name: \"Sustainability\", href: \"/about/sustainability\" },\n      { name: \"Character\", href: \"/about/character\" }\n    ]\n  },\n  {\n    name: \"Businesses\",\n    href: \"/businesses\",\n    dropdown: [\n      { name: \"All Businesses\", href: \"/businesses\" },\n      { name: \"Healthcare\", href: \"/businesses/healthcare\" },\n      { name: \"IT\", href: \"/businesses/it\" },\n      { name: \"Energy\", href: \"/businesses/energy\" },\n      { name: \"Real Estate\", href: \"/businesses/real-estate\" },\n      { name: \"Helicopters\", href: \"/businesses/helicopters\" },\n      { name: \"Food\", href: \"/businesses/food\" }\n    ]\n  },\n  { name: \"Contact\", href: \"/contact\" },\n];\n\nexport function Navbar() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  return (\n    <motion.nav\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.8 }}\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        isScrolled \n          ? \"bg-white/95 backdrop-blur-md shadow-lg border-b border-slate-200/50\" \n          : \"bg-transparent\"\n      }`}\n    >\n      <div className=\"container mx-auto px-6\">\n        <div className=\"flex items-center justify-between h-16 md:h-20\">\n          {/* Logo */}\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            className=\"flex items-center space-x-2 cursor-pointer\"\n          >\n            <TrendingUp className={`h-8 w-8 ${isScrolled ? \"text-teal-600\" : \"text-teal-400\"}`} />\n            <span className={`text-2xl font-bold ${isScrolled ? \"text-slate-900\" : \"text-white\"}`}>\n              Klever<span className={isScrolled ? \"text-teal-600\" : \"text-teal-400\"}>Co</span>\n            </span>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item, index) => (\n              <motion.div\n                key={item.name}\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"relative\"\n                onMouseEnter={() => item.dropdown && setActiveDropdown(item.name)}\n                onMouseLeave={() => setActiveDropdown(null)}\n              >\n                {item.dropdown ? (\n                  <div className=\"relative\">\n                    <button\n                      className={`font-medium transition-colors duration-300 hover:text-teal-500 flex items-center ${\n                        isScrolled ? \"text-slate-700\" : \"text-white\"\n                      }`}\n                    >\n                      {item.name}\n                      <ChevronDown className=\"h-4 w-4 ml-1\" />\n                    </button>\n\n                    <AnimatePresence>\n                      {activeDropdown === item.name && (\n                        <motion.div\n                          initial={{ opacity: 0, y: 10 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          exit={{ opacity: 0, y: 10 }}\n                          transition={{ duration: 0.2 }}\n                          className=\"absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-slate-200 py-2 z-50\"\n                        >\n                          {item.dropdown.map((dropdownItem) => (\n                            <Link\n                              key={dropdownItem.name}\n                              href={dropdownItem.href}\n                              className=\"block px-4 py-2 text-slate-700 hover:bg-teal-50 hover:text-teal-600 transition-colors duration-200\"\n                            >\n                              {dropdownItem.name}\n                            </Link>\n                          ))}\n                        </motion.div>\n                      )}\n                    </AnimatePresence>\n                  </div>\n                ) : (\n                  <Link\n                    href={item.href}\n                    className={`font-medium transition-colors duration-300 hover:text-teal-500 ${\n                      isScrolled ? \"text-slate-700\" : \"text-white\"\n                    }`}\n                  >\n                    {item.name}\n                  </Link>\n                )}\n              </motion.div>\n            ))}\n          </div>\n\n          {/* Desktop CTA */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button\n              variant={isScrolled ? \"outline\" : \"secondary\"}\n              className={`transition-all duration-300 ${\n                isScrolled \n                  ? \"border-teal-600 text-teal-600 hover:bg-teal-600 hover:text-white\" \n                  : \"bg-white/20 text-white border-white/30 hover:bg-white hover:text-slate-900\"\n              }`}\n            >\n              Get Started\n            </Button>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className={`md:hidden p-2 rounded-lg transition-colors duration-300 ${\n              isScrolled ? \"text-slate-700 hover:bg-slate-100\" : \"text-white hover:bg-white/20\"\n            }`}\n          >\n            {isMobileMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      <AnimatePresence>\n        {isMobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"md:hidden bg-white/95 backdrop-blur-md border-t border-slate-200/50\"\n          >\n            <div className=\"container mx-auto px-6 py-4\">\n              <div className=\"flex flex-col space-y-4\">\n                {navItems.map((item, index) => (\n                  <motion.div\n                    key={item.name}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ duration: 0.3, delay: index * 0.1 }}\n                  >\n                    <Link\n                      href={item.href}\n                      onClick={() => setIsMobileMenuOpen(false)}\n                      className=\"text-slate-700 font-medium py-2 hover:text-teal-600 transition-colors duration-300 block\"\n                    >\n                      {item.name}\n                    </Link>\n                  </motion.div>\n                ))}\n                <motion.div\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.3, delay: navItems.length * 0.1 }}\n                  className=\"pt-4 border-t border-slate-200\"\n                >\n                  <Button className=\"w-full bg-teal-600 hover:bg-teal-700 text-white\">\n                    Get Started\n                  </Button>\n                </motion.div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQA,MAAM,WAAW;IACf;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBAAE,MAAM;gBAAY,MAAM;YAAS;YACnC;gBAAE,MAAM;gBAAW,MAAM;YAAiB;YAC1C;gBAAE,MAAM;gBAAkB,MAAM;YAAwB;YACxD;gBAAE,MAAM;gBAAa,MAAM;YAAmB;SAC/C;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBAAE,MAAM;gBAAkB,MAAM;YAAc;YAC9C;gBAAE,MAAM;gBAAc,MAAM;YAAyB;YACrD;gBAAE,MAAM;gBAAM,MAAM;YAAiB;YACrC;gBAAE,MAAM;gBAAU,MAAM;YAAqB;YAC7C;gBAAE,MAAM;gBAAe,MAAM;YAA0B;YACvD;gBAAE,MAAM;gBAAe,MAAM;YAA0B;YACvD;gBAAE,MAAM;gBAAQ,MAAM;YAAmB;SAC1C;IACH;IACA;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAC,4DAA4D,EACtE,aACI,wEACA,kBACJ;;0BAEF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,WAAU;;8CAEV,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAW,CAAC,QAAQ,EAAE,aAAa,kBAAkB,iBAAiB;;;;;;8CAClF,8OAAC;oCAAK,WAAW,CAAC,mBAAmB,EAAE,aAAa,mBAAmB,cAAc;;wCAAE;sDAC/E,8OAAC;4CAAK,WAAW,aAAa,kBAAkB;sDAAiB;;;;;;;;;;;;;;;;;;sCAK3E,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,WAAU;oCACV,cAAc,IAAM,KAAK,QAAQ,IAAI,kBAAkB,KAAK,IAAI;oCAChE,cAAc,IAAM,kBAAkB;8CAErC,KAAK,QAAQ,iBACZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAW,CAAC,iFAAiF,EAC3F,aAAa,mBAAmB,cAChC;;oDAED,KAAK,IAAI;kEACV,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;0DAGzB,8OAAC,yLAAA,CAAA,kBAAe;0DACb,mBAAmB,KAAK,IAAI,kBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,MAAM;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC1B,YAAY;wDAAE,UAAU;oDAAI;oDAC5B,WAAU;8DAET,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,6BAClB,8OAAC,4JAAA,CAAA,UAAI;4DAEH,MAAM,aAAa,IAAI;4DACvB,WAAU;sEAET,aAAa,IAAI;2DAJb,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;6DAYlC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,+DAA+D,EACzE,aAAa,mBAAmB,cAChC;kDAED,KAAK,IAAI;;;;;;mCAhDT,KAAK,IAAI;;;;;;;;;;sCAwDpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,aAAa,YAAY;gCAClC,WAAW,CAAC,4BAA4B,EACtC,aACI,qEACA,8EACJ;0CACH;;;;;;;;;;;sCAMH,8OAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAW,CAAC,wDAAwD,EAClE,aAAa,sCAAsC,gCACnD;sCAED,iCAAmB,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMtE,8OAAC,yLAAA,CAAA,kBAAe;0BACb,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;kDAEhD,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,oBAAoB;4CACnC,WAAU;sDAET,KAAK,IAAI;;;;;;uCAVP,KAAK,IAAI;;;;;8CAclB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,SAAS,MAAM,GAAG;oCAAI;oCAC1D,WAAU;8CAEV,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;kDAAkD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtF", "debugId": null}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/navigation/footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { TrendingUp, Mail, Phone, MapPin, Linkedin, Twitter, Facebook } from \"lucide-react\";\n\nconst footerSections = [\n  {\n    title: \"Company\",\n    links: [\n      { name: \"About Us\", href: \"#about\" },\n      { name: \"Our Team\", href: \"#team\" },\n      { name: \"Careers\", href: \"#careers\" },\n      { name: \"News\", href: \"#news\" },\n    ]\n  },\n  {\n    title: \"Investment\",\n    links: [\n      { name: \"Sectors\", href: \"#sectors\" },\n      { name: \"Portfolio\", href: \"#portfolio\" },\n      { name: \"Process\", href: \"#process\" },\n      { name: \"ESG\", href: \"#esg\" },\n    ]\n  },\n  {\n    title: \"Resources\",\n    links: [\n      { name: \"Research\", href: \"#research\" },\n      { name: \"Reports\", href: \"#reports\" },\n      { name: \"Insights\", href: \"#insights\" },\n      { name: \"Events\", href: \"#events\" },\n    ]\n  }\n];\n\nconst socialLinks = [\n  { icon: Linkedin, href: \"#\", label: \"LinkedIn\" },\n  { icon: Twitter, href: \"#\", label: \"Twitter\" },\n  { icon: Facebook, href: \"#\", label: \"Facebook\" },\n];\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-slate-900 text-white\">\n      <div className=\"container mx-auto px-6 py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"mb-6\"\n            >\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <TrendingUp className=\"h-8 w-8 text-teal-400\" />\n                <span className=\"text-2xl font-bold\">\n                  Klever<span className=\"text-teal-400\">Co</span>\n                </span>\n              </div>\n              <p className=\"text-slate-300 leading-relaxed mb-6\">\n                Leading investment company specializing in strategic partnerships across \n                Healthcare, Technology, Energy, and Agriculture sectors. Building tomorrow's \n                success stories today.\n              </p>\n            </motion.div>\n\n            {/* Contact Info */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.1 }}\n              viewport={{ once: true }}\n              className=\"space-y-3\"\n            >\n              <div className=\"flex items-center space-x-3 text-slate-300\">\n                <MapPin className=\"h-5 w-5 text-teal-400\" />\n                <span>123 Investment Plaza, Financial District, NY 10004</span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-slate-300\">\n                <Phone className=\"h-5 w-5 text-teal-400\" />\n                <span>+****************</span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-slate-300\">\n                <Mail className=\"h-5 w-5 text-teal-400\" />\n                <span><EMAIL></span>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Footer Links */}\n          {footerSections.map((section, index) => (\n            <motion.div\n              key={section.title}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"text-lg font-semibold mb-4 text-white\">\n                {section.title}\n              </h3>\n              <ul className=\"space-y-3\">\n                {section.links.map((link) => (\n                  <li key={link.name}>\n                    <a\n                      href={link.href}\n                      className=\"text-slate-300 hover:text-teal-400 transition-colors duration-300\"\n                    >\n                      {link.name}\n                    </a>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.5 }}\n          viewport={{ once: true }}\n          className=\"border-t border-slate-700 mt-12 pt-8\"\n        >\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            {/* Copyright */}\n            <div className=\"text-slate-400 text-sm\">\n              © 2024 KleverCo Investment Company. All rights reserved.\n            </div>\n\n            {/* Social Links */}\n            <div className=\"flex items-center space-x-4\">\n              {socialLinks.map((social, index) => {\n                const Icon = social.icon;\n                return (\n                  <motion.a\n                    key={social.label}\n                    href={social.href}\n                    initial={{ opacity: 0, scale: 0 }}\n                    whileInView={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.3, delay: 0.6 + index * 0.1 }}\n                    viewport={{ once: true }}\n                    whileHover={{ scale: 1.1 }}\n                    className=\"w-10 h-10 bg-slate-800 rounded-full flex items-center justify-center text-slate-400 hover:text-teal-400 hover:bg-slate-700 transition-all duration-300\"\n                    aria-label={social.label}\n                  >\n                    <Icon className=\"h-5 w-5\" />\n                  </motion.a>\n                );\n              })}\n            </div>\n\n            {/* Legal Links */}\n            <div className=\"flex items-center space-x-6 text-sm text-slate-400\">\n              <a href=\"#privacy\" className=\"hover:text-teal-400 transition-colors duration-300\">\n                Privacy Policy\n              </a>\n              <a href=\"#terms\" className=\"hover:text-teal-400 transition-colors duration-300\">\n                Terms of Service\n              </a>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,iBAAiB;IACrB;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;YAAS;YACnC;gBAAE,MAAM;gBAAY,MAAM;YAAQ;YAClC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;SAC/B;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAO,MAAM;YAAO;SAC7B;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAU,MAAM;YAAU;SACnC;IACH;CACD;AAED,MAAM,cAAc;IAClB;QAAE,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAK,OAAO;IAAW;IAC/C;QAAE,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAK,OAAO;IAAU;IAC7C;QAAE,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAK,OAAO;IAAW;CAChD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;oDAAK,WAAU;;wDAAqB;sEAC7B,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAG1C,8OAAC;4CAAE,WAAU;sDAAsC;;;;;;;;;;;;8CAQrD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;wBAMX,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,MAAM,QAAQ;gCAAI;gCACtD,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK;;;;;;kDAEhB,8OAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC;0DACC,cAAA,8OAAC;oDACC,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;+BAXjB,QAAQ,KAAK;;;;;;;;;;;8BA0BxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CAAyB;;;;;;0CAKxC,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;oCACxB,MAAM,OAAO,OAAO,IAAI;oCACxB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCAEP,MAAM,OAAO,IAAI;wCACjB,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM,QAAQ;wCAAI;wCACtD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CAAE,OAAO;wCAAI;wCACzB,WAAU;wCACV,cAAY,OAAO,KAAK;kDAExB,cAAA,8OAAC;4CAAK,WAAU;;;;;;uCAVX,OAAO,KAAK;;;;;gCAavB;;;;;;0CAIF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAqD;;;;;;kDAGlF,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAAqD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9F", "debugId": null}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/sections/about/about-hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { TrendingUp, Award, Globe, Users } from \"lucide-react\";\n\nexport function AboutHero() {\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 overflow-hidden pt-20\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.05%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20\"></div>\n      \n      <div className=\"container mx-auto px-6 relative z-10\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          {/* Main Content */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"mb-12\"\n          >\n            <div className=\"flex items-center justify-center mb-6\">\n              <TrendingUp className=\"h-16 w-16 text-teal-400 mr-4\" />\n              <h1 className=\"text-5xl md:text-7xl font-bold text-white\">\n                About <span className=\"text-teal-400\">KleverCo</span>\n              </h1>\n            </div>\n            <div className=\"h-1 w-32 bg-gradient-to-r from-teal-400 to-blue-400 mx-auto rounded-full mb-8\"></div>\n          </motion.div>\n\n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"text-xl md:text-2xl text-slate-300 mb-12 leading-relaxed max-w-3xl mx-auto\"\n          >\n            A global investment and research boutique firm specializing in strategic investments,\n            PPE manufacturing, and innovative business solutions across international markets.\n          </motion.p>\n\n          {/* Key Highlights */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16\"\n          >\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20\">\n              <Award className=\"h-12 w-12 text-teal-400 mx-auto mb-4\" />\n              <h3 className=\"text-2xl font-bold text-white mb-2\">Excellence</h3>\n              <p className=\"text-slate-300\">Award-winning investment strategies with proven results</p>\n            </div>\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20\">\n              <Globe className=\"h-12 w-12 text-teal-400 mx-auto mb-4\" />\n              <h3 className=\"text-2xl font-bold text-white mb-2\">Global Reach</h3>\n              <p className=\"text-slate-300\">International presence across 45+ countries worldwide</p>\n            </div>\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20\">\n              <Users className=\"h-12 w-12 text-teal-400 mx-auto mb-4\" />\n              <h3 className=\"text-2xl font-bold text-white mb-2\">Expert Team</h3>\n              <p className=\"text-slate-300\">150+ seasoned investment professionals</p>\n            </div>\n          </motion.div>\n\n          {/* CTA */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            className=\"text-center\"\n          >\n            <button className=\"bg-teal-500 hover:bg-teal-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300 shadow-lg hover:shadow-xl\">\n              Discover Our Journey\n            </button>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 1, delay: 1 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <motion.div\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n          className=\"w-6 h-10 border-2 border-white rounded-full flex justify-center\"\n        >\n          <motion.div\n            animate={{ y: [0, 12, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n            className=\"w-1 h-3 bg-white rounded-full mt-2\"\n          />\n        </motion.div>\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAG,WAAU;;gDAA4C;8DAClD,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;8CAG1C,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;sCAMD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAE,WAAU;sDAAiB;;;;;;;;;;;;8CAEhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAE,WAAU;sDAAiB;;;;;;;;;;;;8CAEhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAE,WAAU;sDAAiB;;;;;;;;;;;;;;;;;;sCAKlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAEV,cAAA,8OAAC;gCAAO,WAAU;0CAA+I;;;;;;;;;;;;;;;;;;;;;;0BAQvK,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;oBAAG,OAAO;gBAAE;gBACpC,WAAU;0BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;oBAC5C,WAAU;8BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBAAC;wBACzB,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;wBAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMtB", "debugId": null}}, {"offset": {"line": 1314, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/sections/about/business-divisions.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { TrendingUp, Building, Users, Heart, Globe } from \"lucide-react\";\n\nconst businessDivisions = [\n  {\n    title: \"We Develop Business\",\n    subtitle: \"Facilitate Business Growth\",\n    description: \"Global investment and research boutique firm providing strategic solutions for business development and growth facilitation.\",\n    icon: TrendingUp,\n    color: \"from-blue-500 to-indigo-500\"\n  },\n  {\n    title: \"We Support Business\",\n    subtitle: \"Business Innovation and Support\",\n    description: \"Comprehensive business innovation and support services helping companies achieve sustainable growth and operational excellence.\",\n    icon: Building,\n    color: \"from-green-500 to-emerald-500\"\n  },\n  {\n    title: \"We Advise\",\n    subtitle: \"Expert Financial Advice\",\n    description: \"Expert financial advice and strategic guidance for complex financial decisions and investment opportunities across global markets.\",\n    icon: Users,\n    color: \"from-purple-500 to-violet-500\"\n  },\n  {\n    title: \"We Serve Humanity\",\n    subtitle: \"HEALTH\",\n    description: \"State-of-the-art PPE nitrile glove manufacturing serving global healthcare needs with highest quality standards and safety solutions.\",\n    icon: Heart,\n    color: \"from-red-500 to-pink-500\"\n  }\n];\n\nexport function BusinessDivisions() {\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-slate-50 to-blue-50\">\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold text-slate-900 mb-6\">\n            Our <span className=\"text-teal-500\">Business Divisions</span>\n          </h2>\n          <p className=\"text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed\">\n            KleverCo operates across multiple business verticals, delivering comprehensive \n            solutions that drive growth, innovation, and value creation globally.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16\">\n          {businessDivisions.map((division, index) => {\n            const Icon = division.icon;\n            \n            return (\n              <motion.div\n                key={division.title}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"group\"\n              >\n                <div className=\"bg-white rounded-3xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 h-full\">\n                  {/* Header */}\n                  <div className=\"flex items-start mb-6\">\n                    <div className={`w-16 h-16 bg-gradient-to-r ${division.color} rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300`}>\n                      <Icon className=\"h-8 w-8 text-white\" />\n                    </div>\n                    <div className=\"flex-1\">\n                      <h3 className=\"text-2xl font-bold text-slate-900 group-hover:text-teal-600 transition-colors duration-300 mb-1\">\n                        {division.title}\n                      </h3>\n                      <p className=\"text-teal-600 font-semibold text-lg\">{division.subtitle}</p>\n                    </div>\n                  </div>\n\n                  {/* Description */}\n                  <p className=\"text-slate-600 leading-relaxed mb-6\">\n                    {division.description}\n                  </p>\n\n                  {/* CTA */}\n                  <button className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 bg-gradient-to-r ${division.color} text-white hover:shadow-lg hover:scale-105`}>\n                    Learn More\n                  </button>\n                </div>\n              </motion.div>\n            );\n          })}\n        </div>\n\n        {/* Bottom Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"bg-gradient-to-r from-teal-600 to-blue-600 rounded-3xl p-12 text-white text-center\"\n        >\n          <Globe className=\"h-16 w-16 text-teal-200 mx-auto mb-6\" />\n          <h3 className=\"text-3xl md:text-4xl font-bold mb-6\">\n            Global Presence\n          </h3>\n          <p className=\"text-xl text-teal-100 mb-8 max-w-2xl mx-auto\">\n            Operating across multiple continents with strategic locations for investment, research, and manufacturing excellence.\n          </p>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6\">\n              <div className=\"text-2xl font-bold mb-2\">KLEVER Global</div>\n              <div className=\"text-teal-200\">30N Gould Street, ste R</div>\n              <div className=\"text-teal-200\">Sheridan, WY 82801, USA</div>\n              <div className=\"text-sm text-teal-300 mt-2\">+****************</div>\n            </div>\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6\">\n              <div className=\"text-2xl font-bold mb-2\">KLEVER SDN. BHD.</div>\n              <div className=\"text-teal-200\">21-9 Office Suites, 1 Mont' Kiara</div>\n              <div className=\"text-teal-200\">Mont Kiara, 50480 Kuala Lumpur, Malaysia</div>\n              <div className=\"text-sm text-teal-300 mt-2\">+60 3 6201 5195</div>\n            </div>\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6\">\n              <div className=\"text-2xl font-bold mb-2\">KLEVER Thai Hua Gloves</div>\n              <div className=\"text-teal-200\">238/10 Ratchada-Pisek Road</div>\n              <div className=\"text-teal-200\">Huai-Khwang, Bangkok 10310, Thailand</div>\n              <div className=\"text-sm text-teal-300 mt-2\">Manufacturing Hub</div>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,oBAAoB;IACxB;QACE,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM,kNAAA,CAAA,aAAU;QAChB,OAAO;IACT;IACA;QACE,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;IACT;IACA;QACE,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;IACT;IACA;QACE,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;;gCAAqD;8CAC7D,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAEtC,8OAAC;4BAAE,WAAU;sCAA2D;;;;;;;;;;;;8BAM1E,8OAAC;oBAAI,WAAU;8BACZ,kBAAkB,GAAG,CAAC,CAAC,UAAU;wBAChC,MAAM,OAAO,SAAS,IAAI;wBAE1B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,2BAA2B,EAAE,SAAS,KAAK,CAAC,2GAA2G,CAAC;0DACvK,cAAA,8OAAC;oDAAK,WAAU;;;;;;;;;;;0DAElB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,SAAS,KAAK;;;;;;kEAEjB,8OAAC;wDAAE,WAAU;kEAAuC,SAAS,QAAQ;;;;;;;;;;;;;;;;;;kDAKzE,8OAAC;wCAAE,WAAU;kDACV,SAAS,WAAW;;;;;;kDAIvB,8OAAC;wCAAO,WAAW,CAAC,kFAAkF,EAAE,SAAS,KAAK,CAAC,2CAA2C,CAAC;kDAAE;;;;;;;;;;;;2BA3BlK,SAAS,KAAK;;;;;oBAiCzB;;;;;;8BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAG5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA0B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAI,WAAU;sDAA6B;;;;;;;;;;;;8CAE9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA0B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAI,WAAU;sDAA6B;;;;;;;;;;;;8CAE9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA0B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAI,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1D", "debugId": null}}, {"offset": {"line": 1715, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/sections/about/global-presence.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { MapPin, Phone, Mail } from \"lucide-react\";\n\nconst offices = [\n  {\n    name: \"KLEVER Global\",\n    country: \"United States\",\n    address: \"30N Gould Street, ste R, Sheridan, WY 82801, USA\",\n    phone: \"+****************\",\n    type: \"Global Headquarters\",\n    color: \"from-blue-500 to-indigo-500\"\n  },\n  {\n    name: \"KLEVER SDN. BHD.\",\n    country: \"Malaysia\", \n    address: \"21-9 Office Suites, 1 Mont' Kiara, No 1 Jalan Kiara, Mont Kiara, 50480 Kuala Lumpur, Malaysia\",\n    phone: \"+60 3 6201 5195\",\n    type: \"Regional Operations\",\n    color: \"from-green-500 to-emerald-500\"\n  },\n  {\n    name: \"KLEVER Thai Hua Gloves\",\n    country: \"Thailand\",\n    address: \"238/10 Ratchada-Pisek Road, Huai-Khwang, Bangkok 10310, Thailand\",\n    phone: \"\",\n    type: \"Manufacturing Hub\",\n    color: \"from-red-500 to-pink-500\"\n  }\n];\n\nexport function GlobalPresence() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold text-slate-900 mb-6\">\n            Global <span className=\"text-teal-500\">Presence</span>\n          </h2>\n          <p className=\"text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed\">\n            A global investment and research boutique firm with strategic locations across three continents, \n            ensuring comprehensive coverage and local expertise in key markets.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16\">\n          {offices.map((office, index) => (\n            <motion.div\n              key={office.name}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"group\"\n            >\n              <div className=\"bg-white rounded-3xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 h-full\">\n                {/* Header */}\n                <div className=\"text-center mb-6\">\n                  <div className={`w-16 h-16 bg-gradient-to-r ${office.color} rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>\n                    <MapPin className=\"h-8 w-8 text-white\" />\n                  </div>\n                  <h3 className=\"text-2xl font-bold text-slate-900 group-hover:text-teal-600 transition-colors duration-300 mb-2\">\n                    {office.name}\n                  </h3>\n                  <p className=\"text-teal-600 font-semibold\">{office.type}</p>\n                </div>\n\n                {/* Country */}\n                <div className=\"mb-4\">\n                  <div className=\"text-lg font-bold text-slate-800 mb-2\">{office.country}</div>\n                </div>\n\n                {/* Address */}\n                <div className=\"mb-6\">\n                  <div className=\"flex items-start mb-3\">\n                    <MapPin className=\"h-5 w-5 text-slate-400 mr-3 mt-1 flex-shrink-0\" />\n                    <p className=\"text-slate-600 leading-relaxed\">{office.address}</p>\n                  </div>\n                  \n                  {office.phone && (\n                    <div className=\"flex items-center\">\n                      <Phone className=\"h-5 w-5 text-slate-400 mr-3\" />\n                      <a href={`tel:${office.phone}`} className=\"text-slate-600 hover:text-teal-600 transition-colors duration-300\">\n                        {office.phone}\n                      </a>\n                    </div>\n                  )}\n                </div>\n\n                {/* CTA */}\n                <button className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 bg-gradient-to-r ${office.color} text-white hover:shadow-lg hover:scale-105`}>\n                  Contact Office\n                </button>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Contact Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"bg-gradient-to-r from-slate-50 to-blue-50 rounded-3xl p-12 text-center\"\n        >\n          <Mail className=\"h-16 w-16 text-teal-500 mx-auto mb-6\" />\n          <h3 className=\"text-3xl md:text-4xl font-bold text-slate-900 mb-6\">\n            Get in Touch\n          </h3>\n          <p className=\"text-xl text-slate-700 mb-8 max-w-2xl mx-auto\">\n            Ready to explore investment opportunities or learn more about our services? \n            Contact our team for personalized consultation.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <a \n              href=\"mailto:<EMAIL>\"\n              className=\"bg-teal-500 hover:bg-teal-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300 shadow-lg hover:shadow-xl\"\n            >\n              <EMAIL>\n            </a>\n            <button className=\"border-2 border-teal-500 text-teal-600 hover:bg-teal-500 hover:text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300\">\n              Schedule Consultation\n            </button>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,UAAU;IACd;QACE,MAAM;QACN,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QACN,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;;gCAAqD;8CAC1D,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAEzC,8OAAC;4BAAE,WAAU;sCAA2D;;;;;;;;;;;;8BAM1E,8OAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,2BAA2B,EAAE,OAAO,KAAK,CAAC,mHAAmH,CAAC;0DAC7K,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,8OAAC;gDAAG,WAAU;0DACX,OAAO,IAAI;;;;;;0DAEd,8OAAC;gDAAE,WAAU;0DAA+B,OAAO,IAAI;;;;;;;;;;;;kDAIzD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDAAyC,OAAO,OAAO;;;;;;;;;;;kDAIxE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAE,WAAU;kEAAkC,OAAO,OAAO;;;;;;;;;;;;4CAG9D,OAAO,KAAK,kBACX,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAE,MAAM,CAAC,IAAI,EAAE,OAAO,KAAK,EAAE;wDAAE,WAAU;kEACvC,OAAO,KAAK;;;;;;;;;;;;;;;;;;kDAOrB,8OAAC;wCAAO,WAAW,CAAC,kFAAkF,EAAE,OAAO,KAAK,CAAC,2CAA2C,CAAC;kDAAE;;;;;;;;;;;;2BA1ChK,OAAO,IAAI;;;;;;;;;;8BAmDtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAGnE,8OAAC;4BAAE,WAAU;sCAAgD;;;;;;sCAI7D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCAAO,WAAU;8CAAmJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjL", "debugId": null}}, {"offset": {"line": 2057, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/sections/about/company-values.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Shield, Users, Zap, Heart, Globe, Award } from \"lucide-react\";\n\nconst values = [\n  {\n    icon: Shield,\n    title: \"Trust & Reliability\",\n    description: \"Building lasting relationships through consistent delivery and unwavering commitment to our partners' success.\",\n    color: \"from-blue-500 to-indigo-500\"\n  },\n  {\n    icon: Users,\n    title: \"Collaborative Partnership\",\n    description: \"Working hand-in-hand with entrepreneurs and management teams to achieve shared goals and mutual growth.\",\n    color: \"from-teal-500 to-cyan-500\"\n  },\n  {\n    icon: Zap,\n    title: \"Agile Excellence\",\n    description: \"Adapting quickly to market changes while maintaining the highest standards of investment discipline.\",\n    color: \"from-yellow-500 to-orange-500\"\n  },\n  {\n    icon: Heart,\n    title: \"Passionate Commitment\",\n    description: \"Bringing genuine enthusiasm and dedication to every investment opportunity and partnership we pursue.\",\n    color: \"from-red-500 to-pink-500\"\n  },\n  {\n    icon: Globe,\n    title: \"Global Mindset\",\n    description: \"Leveraging international perspectives and cross-border expertise to unlock worldwide opportunities.\",\n    color: \"from-green-500 to-emerald-500\"\n  },\n  {\n    icon: Award,\n    title: \"Performance Excellence\",\n    description: \"Consistently delivering superior results through rigorous analysis, strategic thinking, and operational excellence.\",\n    color: \"from-purple-500 to-violet-500\"\n  }\n];\n\nexport function CompanyValues() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold text-slate-900 mb-6\">\n            Our <span className=\"text-teal-500\">Values</span>\n          </h2>\n          <p className=\"text-xl text-slate-600 max-w-3xl mx-auto\">\n            The core values that define our culture, guide our decisions, and shape our relationships \n            with partners, portfolio companies, and stakeholders worldwide.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {values.map((value, index) => {\n            const Icon = value.icon;\n            \n            return (\n              <motion.div\n                key={value.title}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"group\"\n              >\n                <div className=\"bg-white rounded-2xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 h-full\">\n                  <div className={`w-16 h-16 bg-gradient-to-r ${value.color} rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>\n                    <Icon className=\"h-8 w-8 text-white\" />\n                  </div>\n                  \n                  <h3 className=\"text-xl font-bold text-slate-900 mb-4 group-hover:text-teal-600 transition-colors duration-300\">\n                    {value.title}\n                  </h3>\n                  \n                  <p className=\"text-slate-600 leading-relaxed\">\n                    {value.description}\n                  </p>\n                  \n                  {/* Decorative element */}\n                  <div className={`w-full h-1 bg-gradient-to-r ${value.color} rounded-full mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300`}></div>\n                </div>\n              </motion.div>\n            );\n          })}\n        </div>\n\n        {/* Bottom CTA Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-20\"\n        >\n          <div className=\"bg-gradient-to-r from-teal-600 to-blue-600 rounded-3xl p-12 text-white\">\n            <h3 className=\"text-3xl md:text-4xl font-bold mb-6\">\n              Experience Our Values in Action\n            </h3>\n            <p className=\"text-xl text-teal-100 mb-8 max-w-2xl mx-auto\">\n              See how our values translate into exceptional results and lasting partnerships \n              across our diverse portfolio of successful investments.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"bg-white text-teal-600 hover:bg-teal-50 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300 shadow-lg\">\n                View Success Stories\n              </button>\n              <button className=\"border-2 border-white text-white hover:bg-white hover:text-teal-600 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300\">\n                Partner With Us\n              </button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,SAAS;IACb;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;;gCAAqD;8CAC7D,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAEtC,8OAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;;8BAM1D,8OAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC,OAAO;wBAClB,MAAM,OAAO,MAAM,IAAI;wBAEvB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC,2BAA2B,EAAE,MAAM,KAAK,CAAC,2GAA2G,CAAC;kDACpK,cAAA,8OAAC;4CAAK,WAAU;;;;;;;;;;;kDAGlB,8OAAC;wCAAG,WAAU;kDACX,MAAM,KAAK;;;;;;kDAGd,8OAAC;wCAAE,WAAU;kDACV,MAAM,WAAW;;;;;;kDAIpB,8OAAC;wCAAI,WAAW,CAAC,4BAA4B,EAAE,MAAM,KAAK,CAAC,oFAAoF,CAAC;;;;;;;;;;;;2BArB7I,MAAM,KAAK;;;;;oBAyBtB;;;;;;8BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAI5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAA8H;;;;;;kDAGhJ,8OAAC;wCAAO,WAAU;kDAA6I;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7K", "debugId": null}}]}