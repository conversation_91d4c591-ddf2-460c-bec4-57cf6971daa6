"use client";

import { motion } from "framer-motion";
import { Target, Eye, Compass, Lightbulb } from "lucide-react";

export function MissionVision() {
  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
            Our Purpose & <span className="text-teal-500">Vision</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            Guided by unwavering principles and a clear vision for the future of strategic investment.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Mission */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="bg-white rounded-3xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-shadow duration-300"
          >
            <div className="flex items-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-r from-teal-500 to-cyan-500 rounded-full flex items-center justify-center mr-4">
                <Target className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-3xl font-bold text-slate-900">Our Mission</h3>
            </div>
            <p className="text-lg text-slate-700 leading-relaxed mb-6">
              To empower visionary entrepreneurs and innovative companies by providing strategic capital, 
              expert guidance, and transformative partnerships that drive sustainable growth and create 
              lasting value for all stakeholders.
            </p>
            <div className="space-y-3">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-teal-500 rounded-full mr-3"></div>
                <span className="text-slate-600">Strategic capital deployment</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-teal-500 rounded-full mr-3"></div>
                <span className="text-slate-600">Expert operational guidance</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-teal-500 rounded-full mr-3"></div>
                <span className="text-slate-600">Sustainable value creation</span>
              </div>
            </div>
          </motion.div>

          {/* Vision */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="bg-white rounded-3xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-shadow duration-300"
          >
            <div className="flex items-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mr-4">
                <Eye className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-3xl font-bold text-slate-900">Our Vision</h3>
            </div>
            <p className="text-lg text-slate-700 leading-relaxed mb-6">
              To be the world's most trusted and innovative investment partner, recognized for our 
              ability to identify and nurture tomorrow's industry leaders while setting new standards 
              for responsible and impactful investing.
            </p>
            <div className="space-y-3">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                <span className="text-slate-600">Global investment leadership</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                <span className="text-slate-600">Innovation-driven partnerships</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                <span className="text-slate-600">Responsible investing standards</span>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Core Principles */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="bg-gradient-to-r from-slate-900 to-blue-900 rounded-3xl p-12 text-white"
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold mb-4">
              Our Core <span className="text-teal-400">Principles</span>
            </h3>
            <p className="text-xl text-slate-300 max-w-2xl mx-auto">
              The fundamental beliefs that guide every decision and partnership we make.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-teal-500 rounded-full flex items-center justify-center flex-shrink-0">
                <Compass className="h-6 w-6 text-white" />
              </div>
              <div>
                <h4 className="text-xl font-bold mb-2">Integrity First</h4>
                <p className="text-slate-300">
                  We conduct business with the highest ethical standards, transparency, 
                  and accountability in all our relationships.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                <Lightbulb className="h-6 w-6 text-white" />
              </div>
              <div>
                <h4 className="text-xl font-bold mb-2">Innovation Excellence</h4>
                <p className="text-slate-300">
                  We embrace cutting-edge thinking and transformative technologies 
                  to drive breakthrough results.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                <Target className="h-6 w-6 text-white" />
              </div>
              <div>
                <h4 className="text-xl font-bold mb-2">Long-term Focus</h4>
                <p className="text-slate-300">
                  We build enduring partnerships and sustainable value creation 
                  that benefits all stakeholders over time.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                <Eye className="h-6 w-6 text-white" />
              </div>
              <div>
                <h4 className="text-xl font-bold mb-2">Global Perspective</h4>
                <p className="text-slate-300">
                  We leverage our international expertise and network to identify 
                  opportunities across global markets.
                </p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
