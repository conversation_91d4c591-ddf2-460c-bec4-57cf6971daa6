"use client";

import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight, Globe, Shield, Factory, Truck } from "lucide-react";

export function Gloves() {
  const [currentSlide, setCurrentSlide] = useState(0);

  const locations = [
    {
      country: "USA",
      entity: "Kleverco",
      description: "Klever Global is a registered entity in United States of America. It is the Marketing and Sales arm for Americas, Europe and Australia. It is the face of Klever in these markets to supplies and distribute our Products.",
      role: "Marketing & Sales"
    },
    {
      country: "MALAYSIA", 
      entity: "Kleverco",
      description: "Klever Sdn. Bhd. is a Malaysian domiciled entity. It is the Headquarter of Klever. It is an umbrella establishment which has several Manufacturing units under it across South East Asia. It is our Operational pivot for Klever Gloves.",
      role: "Headquarters & Operations"
    },
    {
      country: "THAILAND",
      entity: "Kleverco", 
      description: "Klever Thai Hua Gloves Co. Ltd. is established in Kingdom of Thailand. It is a Joint Venture Company with Thai Hua Rubber Public Company Limited. It is our Partnership entity which one of the legendary pioneer glove manufacturer.",
      role: "Joint Venture Manufacturing"
    }
  ];

  const processSteps = [
    {
      title: "Cleaning the Formers",
      description: "Ceramic, hand-shaped formers are run through water and acid to clean and remove residue, then dried to remove remaining liquids"
    },
    {
      title: "Preparing the Formers", 
      description: "Formers are dipped in calcium carbonate and calcium nitrate mixture to help nitrile solidify, then dried again"
    },
    {
      title: "Dipping in Nitrile",
      description: "Formers are dipped into NBR compound tank, which may include other additives and colors for specific requirements"
    },
    {
      title: "Vulcanizing Nitrile Rubber",
      description: "NBR coating is heated at high temperature to form the gloves as they dry through vulcanization process"
    },
    {
      title: "Leaching the Gloves",
      description: "Gloves are dipped in water tanks to remove all residues and extractable chemicals, crucial for medical and food-grade gloves"
    }
  ];

  const offerings = [
    { term: "EXW", description: "Ex Works" },
    { term: "FOB", description: "Free on Board" },
    { term: "CIF", description: "Cost, Insurance & Freight" },
    { term: "CIF DDP", description: "Delivered Duty Paid - complete supply chain from beginning to end, delivered to your doorstep" }
  ];

  // Auto-advance slideshow
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % locations.length);
    }, 5000);
    return () => clearInterval(timer);
  }, [locations.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % locations.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + locations.length) % locations.length);
  };

  return (
    <section className="py-32 bg-white">
      <div className="container mx-auto px-6">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-8 tracking-tight">
              GLOVES
            </h2>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto mb-8"></div>
            <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed font-light">
              State-of-the-art PPE nitrile glove manufacturing with global distribution capabilities
            </p>
          </motion.div>

          {/* Introduction */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-20"
          >
            <div className="bg-slate-50 rounded-2xl p-12 border border-slate-100">
              <div className="text-center mb-12">
                <h3 className="text-3xl font-light text-slate-900 mb-6 tracking-tight">
                  Klever Gloves
                </h3>
                <div className="w-16 h-px bg-slate-300 mx-auto mb-6"></div>
              </div>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <div>
                  <p className="text-lg text-slate-700 leading-relaxed font-light mb-6">
                    Klever has been engaged over the years as an Investment Advisory and Management entity. The current COVID-19 pandemic made us endeavor to our Corporate Social Responsibility. We took the initiative to participate and play our role towards alleviating current prevalent pandemic by contributing towards saving lives.
                  </p>
                  <p className="text-slate-600 leading-relaxed font-light">
                    We at Klever take pride to introduce our own Personal Protective Equipment Product "Klever Gloves". We present to partner with our customers and consumers so we may consistently deliver innovative, high quality standard gloves at the most competitive prices.
                  </p>
                </div>
                
                <div className="bg-white rounded-xl p-8 shadow-lg">
                  <h4 className="text-xl font-medium text-slate-900 mb-6">Our Commitment</h4>
                  <div className="space-y-4">
                    {[
                      "Innovative, high quality standards",
                      "Most competitive prices",
                      "Vertical integrated model",
                      "Global footprint expansion",
                      "Easy customer access"
                    ].map((commitment, index) => (
                      <div key={commitment} className="flex items-center">
                        <div className="w-2 h-2 bg-slate-900 rounded-full mr-3"></div>
                        <span className="text-slate-700 font-light">{commitment}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Global Locations Slideshow */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="mb-20"
          >
            <div className="text-center mb-16">
              <h3 className="text-3xl font-light text-slate-900 mb-8 tracking-tight">
                Global <span className="font-normal text-slate-600">Presence</span>
              </h3>
            </div>

            <div className="relative bg-slate-50 rounded-2xl overflow-hidden">
              <div className="relative aspect-[16/8] flex items-center justify-center p-12">
                {/* Current Location Display */}
                <div className="text-center max-w-4xl">
                  <div className="text-4xl font-light text-slate-900 mb-4 tracking-tight">
                    {locations[currentSlide].country}
                  </div>
                  <div className="text-xl text-slate-600 mb-6 font-light">
                    {locations[currentSlide].role}
                  </div>
                  <p className="text-lg text-slate-700 leading-relaxed font-light">
                    {locations[currentSlide].description}
                  </p>
                </div>

                {/* Navigation Arrows */}
                <button
                  onClick={prevSlide}
                  className="absolute left-6 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white p-3 rounded-full shadow-lg transition-all duration-300"
                >
                  <ChevronLeft className="h-6 w-6 text-slate-700" />
                </button>
                <button
                  onClick={nextSlide}
                  className="absolute right-6 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white p-3 rounded-full shadow-lg transition-all duration-300"
                >
                  <ChevronRight className="h-6 w-6 text-slate-700" />
                </button>

                {/* Slide Indicators */}
                <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  {locations.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentSlide(index)}
                      className={`w-3 h-3 rounded-full transition-all duration-300 ${
                        index === currentSlide ? "bg-slate-900" : "bg-slate-400"
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>
          </motion.div>

          {/* PPE Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
            className="mb-20"
          >
            <div className="bg-slate-900 rounded-2xl p-12 text-white">
              <h3 className="text-3xl font-light mb-6 tracking-tight">
                Personal Protective Equipment
              </h3>
              <div className="w-16 h-px bg-slate-400 mb-8"></div>
              <p className="text-slate-300 leading-relaxed font-light text-lg mb-8">
                Personal Protective Equipment (PPE) encompasses protective clothing, helmets, gloves, face shields, goggles, facemasks, respirators and other specifically designed equipment for protection in work environment, spread over various industries from medical facilities to manufacturing units.
              </p>
              <p className="text-slate-300 leading-relaxed font-light">
                PPE at medical care facilities protects users from exchange of contagious material and shields patients from contracting pathogens from environment in medical facilities.
              </p>
            </div>
          </motion.div>

          {/* Manufacturing Process */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="mb-20"
          >
            <div className="text-center mb-16">
              <h3 className="text-3xl font-light text-slate-900 mb-8 tracking-tight">
                Manufacturing <span className="font-normal text-slate-600">Process</span>
              </h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {processSteps.map((step, index) => (
                <motion.div
                  key={step.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-xl p-8 shadow-lg border border-slate-100"
                >
                  <div className="bg-slate-900 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium mb-6">
                    {index + 1}
                  </div>
                  <h4 className="text-lg font-medium text-slate-900 mb-4">
                    {step.title}
                  </h4>
                  <p className="text-slate-600 font-light leading-relaxed text-sm">
                    {step.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Business Model & Offerings */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Business Model */}
              <div className="bg-slate-50 rounded-2xl p-8 border border-slate-100">
                <h3 className="text-2xl font-medium text-slate-900 mb-6">
                  Vertically Integrated Business Model
                </h3>
                <div className="w-16 h-px bg-slate-300 mb-6"></div>
                <p className="text-slate-700 leading-relaxed font-light mb-6">
                  Klever operates on a vertically integrated Business Model. We are into Nitrile Glove business from A to Z. We cover from raw material sourcing, to owned production lines for the production, to logistics partnering, to marketing strategies all the way to sales network and supplying of the Product.
                </p>
                <p className="text-slate-600 leading-relaxed font-light">
                  We cater, supervise and live a complete cycle of Supply Chain.
                </p>
              </div>

              {/* Our Offerings */}
              <div className="bg-white rounded-2xl p-8 shadow-lg border border-slate-100">
                <h3 className="text-2xl font-medium text-slate-900 mb-6">
                  Our Offerings
                </h3>
                <div className="w-16 h-px bg-slate-300 mb-6"></div>
                <p className="text-slate-700 leading-relaxed font-light mb-6">
                  We supply our various products at different levels of supply chain:
                </p>
                <div className="space-y-4">
                  {offerings.map((offering, index) => (
                    <div key={offering.term} className="flex items-start">
                      <div className="bg-slate-900 text-white px-3 py-1 rounded text-sm font-medium mr-4 mt-1 flex-shrink-0">
                        {offering.term}
                      </div>
                      <p className="text-slate-600 font-light text-sm leading-relaxed">
                        {offering.description}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
