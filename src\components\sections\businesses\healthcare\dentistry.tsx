"use client";

import { motion } from "framer-motion";
import { MapPin, Users, Award, Heart } from "lucide-react";

export function Dentistry() {
  const locations = [
    {
      name: "Bletchingley",
      address: "The Barn, High St, Bletchingley, Redhill RH1 4PA, United Kingdom",
      description: "Located in the beautiful village of Bletchingley, near Redhill"
    },
    {
      name: "East Grinstead", 
      address: "138 Holtye Rd, East Grinstead RH19 3EA, United Kingdom",
      description: "Situated in East Grinstead near Newchapel"
    },
    {
      name: "South Croydon",
      address: "337 limpsfield Road CR2 9BY, United Kingdom", 
      description: "Located in South Croydon next to Farleigh Golf Club"
    }
  ];

  const specialties = [
    {
      icon: Heart,
      title: "Emergency Care",
      description: "Comprehensive dental emergencies and trauma treatment with immediate response capabilities"
    },
    {
      icon: Users,
      title: "Special Needs",
      description: "Specialized care for nervous patients and people with learning difficulties"
    },
    {
      icon: Award,
      title: "Master's Level Clinicians",
      description: "Experienced professionals with Master's qualifications and decades of global experience"
    },
    {
      icon: MapPin,
      title: "Multiple Specialties",
      description: "Several specialties available under each roof with multiple operatories"
    }
  ];

  return (
    <section className="py-32 bg-slate-50">
      <div className="container mx-auto px-6">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-8 tracking-tight">
              Aesthetica Dental Clinique
            </h2>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto mb-8"></div>
            <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed font-light">
              Boutique dental clinics delivering finest private dental care across Surrey, England
            </p>
          </motion.div>

          {/* Main Description */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-20"
          >
            <div className="bg-white rounded-2xl p-12 shadow-lg border border-slate-100">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-3xl font-light text-slate-900 mb-6 tracking-tight">
                    Excellence in Dental Care
                  </h3>
                  <div className="w-16 h-px bg-slate-300 mb-6"></div>
                  <p className="text-lg text-slate-700 leading-relaxed font-light mb-6">
                    Aesthetica has three locations in beautiful lush greens of Surrey, England. They are boutique dental clinics delivering one of the finest private Dental Care.
                  </p>
                  <p className="text-slate-600 leading-relaxed font-light">
                    Each patient receives attention to detail with customized, well thought through treatment plans. Multiple operatories offer several specialties under each roof with Master's level qualification clinicians having experiences spanning over more than couple of decades each spread globally.
                  </p>
                </div>
                
                <div className="bg-slate-50 rounded-xl p-8">
                  <h4 className="text-xl font-medium text-slate-900 mb-6">Our Commitment</h4>
                  <div className="space-y-4">
                    {[
                      "Customized treatment plans",
                      "Attention to detail for each patient", 
                      "Multiple specialties under one roof",
                      "Master's level qualified clinicians",
                      "Decades of global experience",
                      "Boutique clinic atmosphere"
                    ].map((commitment, index) => (
                      <div key={commitment} className="flex items-center">
                        <div className="w-2 h-2 bg-slate-900 rounded-full mr-3"></div>
                        <span className="text-slate-700 font-light">{commitment}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Specialties */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="mb-20"
          >
            <div className="text-center mb-16">
              <h3 className="text-3xl font-light text-slate-900 mb-8 tracking-tight">
                Our <span className="font-normal text-slate-600">Specialties</span>
              </h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {specialties.map((specialty, index) => {
                const Icon = specialty.icon;
                
                return (
                  <motion.div
                    key={specialty.title}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="bg-white rounded-xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300 text-center"
                  >
                    <div className="bg-slate-900 p-4 rounded-xl w-fit mx-auto mb-6">
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <h4 className="text-lg font-medium text-slate-900 mb-4">
                      {specialty.title}
                    </h4>
                    <p className="text-slate-600 font-light text-sm leading-relaxed">
                      {specialty.description}
                    </p>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Locations */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <div className="text-center mb-16">
              <h3 className="text-3xl font-light text-slate-900 mb-8 tracking-tight">
                Our <span className="font-normal text-slate-600">Locations</span>
              </h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {locations.map((location, index) => (
                <motion.div
                  key={location.name}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300"
                >
                  <div className="flex items-center mb-6">
                    <div className="bg-slate-900 p-3 rounded-xl mr-4">
                      <MapPin className="h-6 w-6 text-white" />
                    </div>
                    <h4 className="text-xl font-medium text-slate-900">
                      {location.name}
                    </h4>
                  </div>
                  
                  <p className="text-slate-600 font-light mb-4 leading-relaxed">
                    {location.description}
                  </p>
                  
                  <div className="bg-slate-50 rounded-lg p-4">
                    <p className="text-slate-700 font-light text-sm leading-relaxed">
                      {location.address}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Additional Services */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="mt-20"
          >
            <div className="bg-slate-900 rounded-2xl p-12 text-white text-center">
              <h3 className="text-3xl font-light mb-6 tracking-tight">
                Comprehensive Care
              </h3>
              <div className="w-16 h-px bg-slate-400 mx-auto mb-8"></div>
              <p className="text-slate-300 leading-relaxed font-light text-lg max-w-3xl mx-auto">
                We deal with dental emergencies and traumas, nervous patients and people with learning difficulties. Our experienced team provides personalized care in a comfortable, boutique environment across all three Surrey locations.
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
