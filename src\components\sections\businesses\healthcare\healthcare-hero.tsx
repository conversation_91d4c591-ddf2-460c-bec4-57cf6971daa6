"use client";

import { motion } from "framer-motion";
import Image from "next/image";

export function HealthcareHero() {
  return (
    <section className="relative pt-32 pb-20 bg-gradient-to-b from-slate-900 to-slate-800 overflow-hidden">
      {/* Subtle background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,.1)_50%,transparent_75%,transparent_100%)] bg-[length:20px_20px]"></div>
      </div>
      
      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Company Logo */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <div className="flex justify-center mb-8">
              <Image
                src="/images/logo-klever.png"
                alt="KleverCo Logo"
                width={200}
                height={80}
                className="h-20 w-auto"
              />
            </div>
          </motion.div>

          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <h1 className="text-5xl md:text-7xl font-light text-white mb-8 tracking-tight">
              HEALTHCARE
            </h1>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-400 to-transparent mx-auto mb-8"></div>
            <p className="text-2xl text-slate-400 max-w-4xl mx-auto leading-relaxed font-light">
              Comprehensive healthcare solutions spanning telemedicine, manufacturing, and specialized services
            </p>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
