import { Navbar } from "@/components/navigation/navbar";
import { EnergyHero } from "@/components/sections/businesses/energy/energy-hero";
import { EnergyServices } from "@/components/sections/businesses/energy/energy-services";
import { EnergyProducts } from "@/components/sections/businesses/energy/energy-products";
import { EnergyStats } from "@/components/sections/businesses/energy/energy-stats";
import { Footer } from "@/components/navigation/footer";

export const metadata = {
  title: "Energy - KleverCo Global Investment & Research Boutique",
  description: "KleverCo's Energy division specializes in oil & gas, renewable energy, and sustainable energy solutions worldwide.",
};

export default function EnergyPage() {
  return (
    <main className="min-h-screen bg-white">
      <Navbar />
      <EnergyHero />
      <EnergyServices />
      <EnergyProducts />
      <EnergyStats />
      <Footer />
    </main>
  );
}
