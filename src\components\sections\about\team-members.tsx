"use client";

import { motion } from "framer-motion";
import { User, MapPin, Briefcase, GraduationCap, Heart, Building, TrendingUp, Cog, Users } from "lucide-react";
import Image from "next/image";

// Leadership Team
const leadership = [
  {
    name: "<PERSON>",
    role: "CEO",
    description: "Consultant in Merger and Acquisitions. Expert in formulating Strategic Public Policy. Advisor in Public Diplomacy. Neo niche Identifier and Developer from conventional to futuristic tech world. Carrying clout across continents.",
    icon: User,
  },
  {
    name: "Dr. <PERSON>",
    role: "Strategic Advisor",
    description: "A trendsetting conceptualiser. Transaction structuring with progressive creativity expertise. Seasoned persuasive negotiator and a strategist across disciplines. Entrenched in technology space and savvy to this ecosystem.",
    icon: GraduationCap,
  },
  {
    name: "<PERSON>",
    role: "Venture Finance Consultant",
    description: "Consultant in Venture Finance. Specialist in Restructuring Advisory providing governance, oversight and strategic guidance. Expert at Angel Funding, Crowd Funding, Venture Capital and Venture Debt.",
    icon: Briefcase,
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    role: "Aviation Consultant",
    description: "Expert consultant in aviation. Indepth networking in Business Development, Management and Operations, from commercial airlines to airport handling to charter fleet. Member of aviation bars with detailed knowhow of how aviation business runs.",
    icon: Map<PERSON>in,
  },
  {
    name: "<PERSON> <PERSON> Avesani",
    role: "Healthcare Advisor",
    description: "Advisor, researcher and consultant in Healthcare. Have vast exposure to Research, Development and Discovery to medical products and nutrition with emphasis in geriatric and oncological patients. Author to several publications, scientific paper and co-author to books.",
    icon: Heart,
  }
];

// Team Departments
const departments = [
  {
    name: "Investment & Research",
    location: "Global Headquarters, USA",
    members: [
      { name: "Michael Johnson", role: "Senior Investment Analyst", experience: "8 years" },
      { name: "Lisa Wang", role: "Research Director", experience: "12 years" },
      { name: "David Rodriguez", role: "Portfolio Manager", experience: "10 years" },
      { name: "Emily Chen", role: "Investment Associate", experience: "5 years" }
    ],
    color: "from-slate-700 to-slate-800",
    icon: TrendingUp
  },
  {
    name: "Business Development",
    location: "Regional Operations, Malaysia",
    members: [
      { name: "Raj Patel", role: "Business Development Director", experience: "15 years" },
      { name: "Siti Nurhaliza", role: "Strategic Partnerships Manager", experience: "9 years" },
      { name: "James Lim", role: "Market Expansion Lead", experience: "7 years" },
      { name: "Priya Sharma", role: "Client Relations Manager", experience: "6 years" }
    ],
    color: "from-slate-600 to-slate-700",
    icon: Building
  },
  {
    name: "Manufacturing & Operations",
    location: "Manufacturing Hub, Thailand",
    members: [
      { name: "Thanawat Srisuk", role: "Manufacturing Director", experience: "18 years" },
      { name: "Niran Chaiyo", role: "Quality Control Manager", experience: "11 years" },
      { name: "Apinya Thanakit", role: "Production Supervisor", experience: "8 years" },
      { name: "Somkid Rattana", role: "Supply Chain Coordinator", experience: "6 years" }
    ],
    color: "from-slate-800 to-slate-900",
    icon: Cog
  },
  {
    name: "Advisory & Consulting",
    location: "Multi-Location Team",
    members: [
      { name: "Dr. Robert Kim", role: "Senior Financial Advisor", experience: "20 years" },
      { name: "Sarah Mitchell", role: "Strategic Consultant", experience: "14 years" },
      { name: "Hassan Al-Rashid", role: "International Markets Advisor", experience: "16 years" },
      { name: "Maria Gonzalez", role: "Risk Management Specialist", experience: "9 years" }
    ],
    color: "from-slate-700 to-slate-800",
    icon: Users
  }
];

export function TeamMembers() {
  return (
    <section className="py-32 bg-white">
      <div className="container mx-auto px-6">
        <div className="max-w-6xl mx-auto">
          {/* Company Logo */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <div className="flex justify-center mb-8">
              <Image
                src="/images/logo-klever.png"
                alt="KleverCo Logo"
                width={200}
                height={80}
                className="h-20 w-auto"
              />
            </div>
          </motion.div>

          {/* Leadership Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-6 tracking-tight">
              Leadership <span className="font-normal text-slate-600">Team</span>
            </h2>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto mb-8"></div>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed font-light">
              Our distinguished leadership team brings decades of combined experience across
              investment, technology, healthcare, and global business development.
            </p>
          </motion.div>

          {/* Leadership Grid */}
          <div className="space-y-12 mb-32">
            {leadership.map((member, index) => {
              const Icon = member.icon;

              return (
                <motion.div
                  key={member.name}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="group"
                >
                  <div className="bg-slate-50 border border-slate-100 rounded-2xl p-8 md:p-12 hover:shadow-lg hover:border-slate-200 transition-all duration-300">
                    <div className="flex flex-col md:flex-row md:items-start gap-8">
                      {/* Icon & Basic Info */}
                      <div className="flex-shrink-0">
                        <div className="w-16 h-16 bg-slate-900 rounded-xl flex items-center justify-center mb-4 group-hover:bg-slate-800 transition-colors duration-300">
                          <Icon className="h-8 w-8 text-white" />
                        </div>
                        <h3 className="text-2xl font-semibold text-slate-900 mb-2 group-hover:text-slate-800 transition-colors duration-300">
                          {member.name}
                        </h3>
                        <p className="text-slate-600 font-medium mb-4">{member.role}</p>
                      </div>

                      {/* Description */}
                      <div className="flex-1">
                        <p className="text-slate-700 leading-relaxed font-light text-lg">
                          {member.description}
                        </p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>

          {/* Team Departments Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-6 tracking-tight">
              Our <span className="font-normal text-slate-600">Team</span>
            </h2>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto mb-8"></div>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed font-light">
              Dedicated professionals across four key departments, working together to deliver
              exceptional results for our clients and partners worldwide.
            </p>
          </motion.div>

          {/* Team Departments Grid */}
          <div className="space-y-12">
            {departments.map((department, deptIndex) => {
              const Icon = department.icon;

              return (
                <motion.div
                  key={department.name}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: deptIndex * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-slate-50 rounded-3xl p-8 border border-slate-100 hover:shadow-lg hover:border-slate-200 transition-all duration-300"
                >
                  {/* Department Header */}
                  <div className="text-center mb-8">
                    <div className={`w-16 h-16 bg-gradient-to-r ${department.color} rounded-xl flex items-center justify-center mx-auto mb-4`}>
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-2xl md:text-3xl font-semibold text-slate-900 mb-2">
                      {department.name}
                    </h3>
                    <div className="flex items-center justify-center text-slate-600">
                      <MapPin className="h-4 w-4 mr-2" />
                      <span className="font-light">{department.location}</span>
                    </div>
                  </div>

                  {/* Team Members Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {department.members.map((member, memberIndex) => (
                      <motion.div
                        key={member.name}
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: (deptIndex * 0.1) + (memberIndex * 0.05) }}
                        viewport={{ once: true }}
                        className="group"
                      >
                        <div className="bg-white rounded-2xl p-6 border border-slate-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 text-center">
                          {/* Avatar */}
                          <div className={`w-16 h-16 bg-gradient-to-r ${department.color} rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                            <span className="text-white text-lg font-bold">
                              {member.name.split(' ').map(n => n[0]).join('')}
                            </span>
                          </div>

                          {/* Member Info */}
                          <h4 className="text-lg font-semibold text-slate-900 group-hover:text-slate-700 transition-colors duration-300 mb-2">
                            {member.name}
                          </h4>
                          <p className="text-slate-600 font-medium text-sm mb-2">{member.role}</p>
                          <p className="text-slate-500 text-xs font-light">{member.experience} experience</p>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              );
            })}
          </div>

          {/* Bottom Statement */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-center mt-20"
          >
            <div className="bg-slate-900 rounded-3xl p-12 md:p-16">
              <blockquote className="text-2xl md:text-3xl font-light text-white leading-relaxed mb-8">
                "Excellence is achieved through the collective expertise and unwavering
                commitment of our distinguished team members."
              </blockquote>
              <div className="w-16 h-px bg-slate-600 mx-auto mb-6"></div>
              <p className="text-slate-400 font-light">KleverCo Leadership</p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
