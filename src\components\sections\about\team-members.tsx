"use client";

import { motion } from "framer-motion";
import { MapPin, Briefcase } from "lucide-react";

const departments = [
  {
    name: "Investment & Research",
    location: "Global Headquarters, USA",
    members: [
      { name: "<PERSON>", role: "Senior Investment Analyst", experience: "8 years" },
      { name: "<PERSON>", role: "Research Director", experience: "12 years" },
      { name: "<PERSON>", role: "Portfolio Manager", experience: "10 years" },
      { name: "<PERSON>", role: "Investment Associate", experience: "5 years" }
    ],
    color: "from-blue-500 to-indigo-500"
  },
  {
    name: "Business Development",
    location: "Regional Operations, Malaysia",
    members: [
      { name: "<PERSON>", role: "Business Development Director", experience: "15 years" },
      { name: "<PERSON><PERSON>", role: "Strategic Partnerships Manager", experience: "9 years" },
      { name: "<PERSON>", role: "Market Expansion Lead", experience: "7 years" },
      { name: "<PERSON><PERSON>", role: "Client Relations Manager", experience: "6 years" }
    ],
    color: "from-green-500 to-emerald-500"
  },
  {
    name: "Manufacturing & Operations",
    location: "Manufacturing Hub, Thailand",
    members: [
      { name: "<PERSON><PERSON><PERSON>", role: "Manufacturing Director", experience: "18 years" },
      { name: "<PERSON><PERSON>", role: "Quality Control Manager", experience: "11 years" },
      { name: "Apinya Thanakit", role: "Production Supervisor", experience: "8 years" },
      { name: "Somkid Rattana", role: "Supply Chain Coordinator", experience: "6 years" }
    ],
    color: "from-red-500 to-pink-500"
  },
  {
    name: "Advisory & Consulting",
    location: "Multi-Location Team",
    members: [
      { name: "Dr. Robert Kim", role: "Senior Financial Advisor", experience: "20 years" },
      { name: "Sarah Mitchell", role: "Strategic Consultant", experience: "14 years" },
      { name: "Hassan Al-Rashid", role: "International Markets Advisor", experience: "16 years" },
      { name: "Maria Gonzalez", role: "Risk Management Specialist", experience: "9 years" }
    ],
    color: "from-purple-500 to-violet-500"
  }
];

export function TeamMembers() {
  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
            Our <span className="text-teal-500">Team</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Dedicated professionals across four key departments, working together to deliver 
            exceptional results for our clients and partners worldwide.
          </p>
        </motion.div>

        <div className="space-y-12">
          {departments.map((department, deptIndex) => (
            <motion.div
              key={department.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: deptIndex * 0.1 }}
              viewport={{ once: true }}
              className="bg-white rounded-3xl p-8 shadow-lg border border-slate-100"
            >
              {/* Department Header */}
              <div className="text-center mb-8">
                <div className={`w-16 h-16 bg-gradient-to-r ${department.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                  <Briefcase className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-2xl md:text-3xl font-bold text-slate-900 mb-2">
                  {department.name}
                </h3>
                <div className="flex items-center justify-center text-slate-600">
                  <MapPin className="h-4 w-4 mr-2" />
                  <span>{department.location}</span>
                </div>
              </div>

              {/* Team Members Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {department.members.map((member, memberIndex) => (
                  <motion.div
                    key={member.name}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: (deptIndex * 0.1) + (memberIndex * 0.05) }}
                    viewport={{ once: true }}
                    className="group"
                  >
                    <div className="bg-gradient-to-br from-slate-50 to-blue-50 rounded-2xl p-6 border border-slate-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 text-center">
                      {/* Avatar */}
                      <div className={`w-16 h-16 bg-gradient-to-r ${department.color} rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                        <span className="text-white text-lg font-bold">
                          {member.name.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>

                      {/* Member Info */}
                      <h4 className="text-lg font-bold text-slate-900 group-hover:text-teal-600 transition-colors duration-300 mb-2">
                        {member.name}
                      </h4>
                      <p className="text-teal-600 font-semibold text-sm mb-2">{member.role}</p>
                      <p className="text-slate-500 text-xs">{member.experience} experience</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-teal-600 to-blue-600 rounded-3xl p-12 text-white">
            <h3 className="text-3xl md:text-4xl font-bold mb-6">
              Join Our Team
            </h3>
            <p className="text-xl text-teal-100 mb-8 max-w-2xl mx-auto">
              We're always looking for talented individuals to join our growing team 
              across our global offices and diverse business verticals.
            </p>
            <button className="bg-white text-teal-600 hover:bg-teal-50 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300 shadow-lg">
              View Open Positions
            </button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
