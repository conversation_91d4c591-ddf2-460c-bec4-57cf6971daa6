"use client";

import { motion } from "framer-motion";
import { User, MapPin, Briefcase, GraduationCap, Heart } from "lucide-react";
import Image from "next/image";

const members = [
  {
    name: "<PERSON>",
    role: "CEO",
    description: "Consultant in Merger and Acquisitions. Expert in formulating Strategic Public Policy. Advisor in Public Diplomacy. Neo niche Identifier and Developer from conventional to futuristic tech world. Carrying clout across continents.",
    icon: User,
  },
  {
    name: "Dr. <PERSON>",
    role: "Strategic Advisor",
    description: "A trendsetting conceptualiser. Transaction structuring with progressive creativity expertise. Seasoned persuasive negotiator and a strategist across disciplines. Entrenched in technology space and savvy to this ecosystem.",
    icon: GraduationCap,
  },
  {
    name: "<PERSON>",
    role: "Venture Finance Consultant",
    description: "Consultant in Venture Finance. Specialist in Restructuring Advisory providing governance, oversight and strategic guidance. Expert at Angel Funding, Crowd Funding, Venture Capital and Venture Debt.",
    icon: Briefcase,
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    role: "Aviation Consultant",
    description: "Expert consultant in aviation. Indepth networking in Business Development, Management and Operations, from commercial airlines to airport handling to charter fleet. Member of aviation bars with detailed knowhow of how aviation business runs.",
    icon: MapPin,
  },
  {
    name: "<PERSON>",
    role: "Healthcare Advisor",
    description: "Advisor, researcher and consultant in Healthcare. Have vast exposure to Research, Development and Discovery to medical products and nutrition with emphasis in geriatric and oncological patients. Author to several publications, scientific paper and co-author to books.",
    icon: Heart,
  }
];

export function TeamMembers() {
  return (
    <section className="py-32 bg-white">
      <div className="container mx-auto px-6">
        <div className="max-w-6xl mx-auto">
          {/* Company Logo */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <div className="flex justify-center mb-8">
              <Image
                src="/images/logo-klever.png"
                alt="KleverCo Logo"
                width={200}
                height={80}
                className="h-20 w-auto"
              />
            </div>
          </motion.div>

          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-6 tracking-tight">
              Our <span className="font-normal text-slate-600">Members</span>
            </h2>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto mb-8"></div>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed font-light">
              Meet our distinguished team members who lead KleverCo's global operations
              across investment, research, and strategic business development.
            </p>
          </motion.div>

          {/* Members Grid */}
          <div className="space-y-12">
            {members.map((member, index) => {
              const Icon = member.icon;

              return (
                <motion.div
                  key={member.name}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="group"
                >
                  <div className="bg-slate-50 border border-slate-100 rounded-2xl p-8 md:p-12 hover:shadow-lg hover:border-slate-200 transition-all duration-300">
                    <div className="flex flex-col md:flex-row md:items-start gap-8">
                      {/* Icon & Basic Info */}
                      <div className="flex-shrink-0">
                        <div className="w-16 h-16 bg-slate-900 rounded-xl flex items-center justify-center mb-4 group-hover:bg-slate-800 transition-colors duration-300">
                          <Icon className="h-8 w-8 text-white" />
                        </div>
                        <h3 className="text-2xl font-semibold text-slate-900 mb-2 group-hover:text-slate-800 transition-colors duration-300">
                          {member.name}
                        </h3>
                        <p className="text-slate-600 font-medium mb-4">{member.role}</p>
                      </div>

                      {/* Description */}
                      <div className="flex-1">
                        <p className="text-slate-700 leading-relaxed font-light text-lg">
                          {member.description}
                        </p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>

          {/* Bottom Statement */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-center mt-20"
          >
            <div className="bg-slate-900 rounded-3xl p-12 md:p-16">
              <blockquote className="text-2xl md:text-3xl font-light text-white leading-relaxed mb-8">
                "Excellence is achieved through the collective expertise and unwavering
                commitment of our distinguished team members."
              </blockquote>
              <div className="w-16 h-px bg-slate-600 mx-auto mb-6"></div>
              <p className="text-slate-400 font-light">KleverCo Leadership</p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
