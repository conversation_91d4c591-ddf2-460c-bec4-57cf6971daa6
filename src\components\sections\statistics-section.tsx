"use client";

import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useEffect, useState } from "react";
import { 
  Calendar, 
  Users, 
  DollarSign, 
  Building, 
  Globe, 
  TrendingUp 
} from "lucide-react";

const stats = [
  {
    icon: Users,
    value: 50,
    suffix: "M+",
    label: "Patients Served",
    description: "Lives impacted through telemedicine services globally"
  },
  {
    icon: Globe,
    value: 100,
    suffix: "M+",
    label: "PPE Units Produced",
    description: "Critical healthcare protection during global pandemic"
  },
  {
    icon: TrendingUp,
    value: 85,
    suffix: "%",
    label: "Energy Efficiency",
    description: "Renewable energy solutions reducing carbon footprint"
  },
  {
    icon: Building,
    value: 200,
    suffix: "+",
    label: "IT Transformations",
    description: "Digital solutions delivered across enterprises"
  },
  {
    icon: DollarSign,
    value: 15,
    suffix: "B+",
    label: "Economic Impact",
    description: "Value generated across investment portfolio"
  },
  {
    icon: Calendar,
    value: 99,
    suffix: "%",
    label: "COVID Response",
    description: "Healthcare continuity maintained during pandemic"
  }
];

function AnimatedCounter({ 
  value, 
  suffix = "", 
  duration = 2000 
}: { 
  value: number; 
  suffix?: string; 
  duration?: number; 
}) {
  const [count, setCount] = useState(0);
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true });

  useEffect(() => {
    if (isInView) {
      let startTime: number;
      const animate = (currentTime: number) => {
        if (!startTime) startTime = currentTime;
        const progress = Math.min((currentTime - startTime) / duration, 1);
        
        setCount(Math.floor(progress * value));
        
        if (progress < 1) {
          requestAnimationFrame(animate);
        }
      };
      requestAnimationFrame(animate);
    }
  }, [isInView, value, duration]);

  return (
    <span ref={ref}>
      {count}{suffix}
    </span>
  );
}

export function StatisticsSection() {
  return (
    <section className="relative py-24 bg-gradient-to-br from-slate-50 to-blue-50 overflow-hidden">
      {/* Subtle Background Pattern */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23000000%22%20fill-opacity%3D%220.02%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40"></div>

      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-light text-slate-900 mb-6">
            Global <span className="font-medium text-blue-600">Impact</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Measurable results and transformative impact across healthcare, technology, energy, and sustainable development initiatives.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {stats.map((stat, index) => {
            const Icon = stat.icon;

            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center group"
              >
                <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 hover:bg-white/90 hover:shadow-xl transition-all duration-300 border border-white/50 shadow-lg">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Icon className="h-8 w-8 text-blue-600" />
                  </div>

                  <div className="text-4xl md:text-5xl font-light text-slate-900 mb-2">
                    <AnimatedCounter value={stat.value} suffix={stat.suffix} />
                  </div>

                  <h3 className="text-xl font-semibold text-slate-800 mb-3">
                    {stat.label}
                  </h3>

                  <p className="text-slate-600 leading-relaxed">
                    {stat.description}
                  </p>
                </div>
              </motion.div>
            );
          })}
        </div>


      </div>
    </section>
  );
}
