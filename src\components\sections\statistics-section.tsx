"use client";

import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useEffect, useState } from "react";
import { 
  Calendar, 
  Users, 
  DollarSign, 
  Building, 
  Globe, 
  TrendingUp 
} from "lucide-react";

const stats = [
  {
    icon: Calendar,
    value: 25,
    suffix: "+",
    label: "Years of Experience",
    description: "Proven track record in investment excellence"
  },
  {
    icon: Users,
    value: 150,
    suffix: "+",
    label: "Investment Professionals",
    description: "Expert team across global markets"
  },
  {
    icon: DollarSign,
    value: 12.5,
    suffix: "B",
    label: "Assets Under Management",
    description: "Diversified portfolio value"
  },
  {
    icon: Building,
    value: 235,
    suffix: "+",
    label: "Portfolio Companies",
    description: "Strategic investments worldwide"
  },
  {
    icon: Globe,
    value: 45,
    suffix: "+",
    label: "Countries",
    description: "Global investment presence"
  },
  {
    icon: TrendingUp,
    value: 94,
    suffix: "%",
    label: "Success Rate",
    description: "Exceptional investment performance"
  }
];

function AnimatedCounter({ 
  value, 
  suffix = "", 
  duration = 2000 
}: { 
  value: number; 
  suffix?: string; 
  duration?: number; 
}) {
  const [count, setCount] = useState(0);
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true });

  useEffect(() => {
    if (isInView) {
      let startTime: number;
      const animate = (currentTime: number) => {
        if (!startTime) startTime = currentTime;
        const progress = Math.min((currentTime - startTime) / duration, 1);
        
        setCount(Math.floor(progress * value));
        
        if (progress < 1) {
          requestAnimationFrame(animate);
        }
      };
      requestAnimationFrame(animate);
    }
  }, [isInView, value, duration]);

  return (
    <span ref={ref}>
      {count}{suffix}
    </span>
  );
}

export function StatisticsSection() {
  return (
    <section className="relative py-20 bg-gradient-to-br from-teal-600 via-teal-700 to-blue-800 overflow-hidden">
      {/* Mountain Background */}
      <div className="absolute inset-0 opacity-20">
        <svg
          viewBox="0 0 1200 400"
          className="w-full h-full object-cover"
          preserveAspectRatio="xMidYMid slice"
        >
          <path
            d="M0,400 L0,200 L200,100 L400,150 L600,80 L800,120 L1000,60 L1200,100 L1200,400 Z"
            fill="rgba(255,255,255,0.1)"
          />
          <path
            d="M0,400 L0,250 L150,180 L350,220 L550,160 L750,200 L950,140 L1200,180 L1200,400 Z"
            fill="rgba(255,255,255,0.05)"
          />
        </svg>
      </div>

      {/* Geometric Pattern */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%22100%22%20height%3D%22100%22%20viewBox%3D%220%200%20100%20100%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Cpath%20d%3D%22M50%2050L0%200h100L50%2050zM50%2050L100%20100H0L50%2050z%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div>

      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Our <span className="text-teal-300">Impact</span>
          </h2>
          <p className="text-xl text-teal-100 max-w-3xl mx-auto">
            Numbers that reflect our commitment to excellence and our partners' success across global markets.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center group"
              >
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 hover:bg-white/15 transition-all duration-300 border border-white/20">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-teal-400/20 rounded-full mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Icon className="h-8 w-8 text-teal-300" />
                  </div>
                  
                  <div className="text-4xl md:text-5xl font-bold text-white mb-2">
                    <AnimatedCounter value={stat.value} suffix={stat.suffix} />
                  </div>
                  
                  <h3 className="text-xl font-semibold text-teal-100 mb-3">
                    {stat.label}
                  </h3>
                  
                  <p className="text-teal-200/80 leading-relaxed">
                    {stat.description}
                  </p>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">
              Ready to Partner with Excellence?
            </h3>
            <p className="text-teal-100 mb-6">
              Join our network of successful portfolio companies and experience the KleverCo advantage.
            </p>
            <button className="bg-white text-teal-700 px-8 py-3 rounded-lg font-semibold hover:bg-teal-50 transition-colors duration-300">
              Start Your Journey
            </button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
