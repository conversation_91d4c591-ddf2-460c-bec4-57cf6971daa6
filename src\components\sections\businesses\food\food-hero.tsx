"use client";

import { motion } from "framer-motion";
import Image from "next/image";

export function FoodHero() {
  return (
    <section className="relative pt-32 pb-12 overflow-hidden">
      {/* Parallax Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-fixed"
        style={{
          backgroundImage: "url('https://images.unsplash.com/photo-1555939594-58d7cb561ad1?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')"
        }}
      />

      {/* Dark Overlay */}
      <div className="absolute inset-0 bg-slate-900/85" />

      {/* Subtle background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,.1)_50%,transparent_75%,transparent_100%)] bg-[length:20px_20px]"></div>
      </div>
      
      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Company Logo */}
          {/* <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <div className="flex justify-center mb-8">
              <Image
                src="/images/logo-klever.png"
                alt="KleverCo Logo"
                width={200}
                height={80}
                className="h-20 w-auto"
              />
            </div>
          </motion.div> */}

          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <h1 className="text-5xl md:text-7xl font-light text-white mb-8 tracking-tight">
              FOOD
            </h1>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-400 to-transparent mx-auto mb-8"></div>
            <p className="text-2xl text-slate-400 max-w-4xl mx-auto leading-relaxed font-light">
              Exceptional culinary experiences bringing fresh south-asian cuisine to your table
            </p>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
