"use client";

import { motion } from "framer-motion";
import { TrendingUp, Calendar, MapPin, Award, ExternalLink } from "lucide-react";

const featuredInvestments = [
  {
    name: "MedTech Innovations",
    sector: "Healthcare",
    location: "Boston, MA",
    investmentDate: "2021",
    description: "Revolutionary AI-powered diagnostic platform transforming early disease detection with 95% accuracy rate.",
    valuation: "$2.8B",
    growth: "+340%",
    status: "Active",
    highlights: ["FDA Approved", "50M+ Patients Served", "Global Expansion"],
    color: "from-red-500 to-pink-500",
    image: "/api/placeholder/400/300"
  },
  {
    name: "CloudSecure Pro",
    sector: "Technology",
    location: "San Francisco, CA",
    investmentDate: "2020",
    description: "Next-generation cybersecurity platform protecting Fortune 500 companies with zero-breach guarantee.",
    valuation: "$1.9B",
    growth: "+280%",
    status: "Active",
    highlights: ["500+ Enterprise Clients", "99.99% Uptime", "SOC 2 Certified"],
    color: "from-blue-500 to-indigo-500",
    image: "/api/placeholder/400/300"
  },
  {
    name: "GreenEnergy Solutions",
    sector: "Energy",
    location: "Austin, TX",
    investmentDate: "2019",
    description: "Advanced solar technology with 40% higher efficiency, powering sustainable communities worldwide.",
    valuation: "$1.2B",
    growth: "+220%",
    status: "Active",
    highlights: ["1GW+ Installed", "Carbon Neutral", "15 Countries"],
    color: "from-green-500 to-emerald-500",
    image: "/api/placeholder/400/300"
  },
  {
    name: "AgriTech Future",
    sector: "Agriculture",
    location: "Des Moines, IA",
    investmentDate: "2022",
    description: "Precision farming platform using IoT and AI to increase crop yields by 35% while reducing water usage.",
    valuation: "$850M",
    growth: "+180%",
    status: "Active",
    highlights: ["2M+ Acres Managed", "35% Yield Increase", "Water Savings 40%"],
    color: "from-yellow-500 to-orange-500",
    image: "/api/placeholder/400/300"
  }
];

export function FeaturedInvestments() {
  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
            Featured <span className="text-teal-500">Investments</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Spotlight on our most successful portfolio companies that exemplify innovation, 
            growth, and market leadership in their respective sectors.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {featuredInvestments.map((investment, index) => (
            <motion.div
              key={investment.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-white rounded-3xl overflow-hidden shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
                {/* Image Placeholder */}
                <div className={`h-48 bg-gradient-to-r ${investment.color} relative overflow-hidden`}>
                  <div className="absolute inset-0 bg-black/20"></div>
                  <div className="absolute top-4 left-4">
                    <span className="bg-white/20 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-semibold">
                      {investment.sector}
                    </span>
                  </div>
                  <div className="absolute top-4 right-4">
                    <span className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                      {investment.status}
                    </span>
                  </div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-2xl font-bold mb-1">{investment.name}</h3>
                    <div className="flex items-center text-sm opacity-90">
                      <MapPin className="h-4 w-4 mr-1" />
                      {investment.location}
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="p-8">
                  {/* Investment Details */}
                  <div className="flex justify-between items-center mb-6">
                    <div className="flex items-center text-slate-500">
                      <Calendar className="h-4 w-4 mr-2" />
                      <span className="text-sm">Invested {investment.investmentDate}</span>
                    </div>
                    <div className="flex items-center">
                      <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                      <span className="text-green-600 font-semibold">{investment.growth}</span>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-slate-600 leading-relaxed mb-6">
                    {investment.description}
                  </p>

                  {/* Valuation */}
                  <div className="mb-6">
                    <div className="text-3xl font-bold text-slate-900 mb-1">
                      {investment.valuation}
                    </div>
                    <div className="text-slate-500 text-sm">Current Valuation</div>
                  </div>

                  {/* Highlights */}
                  <div className="mb-6">
                    <div className="flex items-center mb-3">
                      <Award className="h-5 w-5 text-teal-500 mr-2" />
                      <span className="font-semibold text-slate-900">Key Achievements</span>
                    </div>
                    <div className="space-y-2">
                      {investment.highlights.map((highlight, idx) => (
                        <div key={idx} className="flex items-center">
                          <div className={`w-2 h-2 bg-gradient-to-r ${investment.color} rounded-full mr-3`}></div>
                          <span className="text-slate-600 text-sm">{highlight}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* CTA */}
                  <div className="flex gap-3">
                    <button className={`flex-1 py-3 rounded-lg font-semibold transition-all duration-300 bg-gradient-to-r ${investment.color} text-white hover:shadow-lg hover:scale-105`}>
                      View Details
                    </button>
                    <button className="w-12 h-12 border-2 border-slate-200 hover:border-teal-500 rounded-lg flex items-center justify-center transition-colors duration-300 group-hover:border-teal-500">
                      <ExternalLink className="h-5 w-5 text-slate-400 group-hover:text-teal-500" />
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-teal-600 to-blue-600 rounded-3xl p-12 text-white">
            <h3 className="text-3xl md:text-4xl font-bold mb-6">
              Explore Our Complete Portfolio
            </h3>
            <p className="text-xl text-teal-100 mb-8 max-w-2xl mx-auto">
              Discover all 235+ portfolio companies and their remarkable growth stories 
              across our diversified investment sectors.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-teal-600 hover:bg-teal-50 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300 shadow-lg">
                View All Investments
              </button>
              <button className="border-2 border-white text-white hover:bg-white hover:text-teal-600 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300">
                Download Portfolio Report
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
