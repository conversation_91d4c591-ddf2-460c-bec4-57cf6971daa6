"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import { Menu, X, ChevronDown, Search } from "lucide-react";

const navItems = [
  { name: "Home", href: "/" },
  {
    name: "About",
    href: "/about",
    dropdown: [
      { name: "About Us", href: "/about" },
      { name: "Members", href: "/about/members" },
      { name: "Sustainability", href: "/about/sustainability" },
      { name: "Character", href: "/about/character" }
    ]
  },
  {
    name: "Businesses",
    href: "/businesses/healthcare",
    dropdown: [
      { name: "Healthcare", href: "/businesses/healthcare" },
      { name: "IT", href: "/businesses/it" },
      { name: "Energy", href: "/businesses/energy" },
      { name: "Real Estate", href: "/businesses/real-estate" },
      { name: "Helicopters", href: "/businesses/helicopters" },
      { name: "Food", href: "/businesses/food" }
    ]
  },
  { name: "Contact", href: "/contact" },
];

export function Navbar() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <motion.nav
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? "bg-[#0B1426]/95 backdrop-blur-md shadow-lg border-b border-slate-300/20"
          : "bg-transparent"
      }`}
    >
      <div className="container mx-auto px-6">
        <div className="flex items-center justify-between h-16 md:h-18">
          {/* Logo */}
          <Link href="/">
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="flex items-center cursor-pointer"
            >
              <Image
                src="/images/logo-klever.png"
                alt="KleverCo Logo"
                width={160}
                height={64}
                className="h-12 md:h-14 w-auto transition-all duration-300"
              />
            </motion.div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-2">
            {navItems.map((item, index) => (
              <motion.div
                key={item.name}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1, ease: "easeOut" }}
                className="relative"
                onMouseEnter={() => item.dropdown && setActiveDropdown(item.name)}
                onMouseLeave={() => setActiveDropdown(null)}
              >
                {item.dropdown ? (
                  <div className="relative">
                    <button
                      className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center ${
                        isScrolled
                          ? "text-slate-200 hover:text-slate-100 hover:bg-slate-700/50"
                          : "text-white/90 hover:text-white hover:bg-white/10"
                      }`}
                    >
                      {item.name}
                      <ChevronDown className={`h-4 w-4 ml-1 transition-transform duration-200 ${
                        activeDropdown === item.name ? "rotate-180" : ""
                      }`} />
                    </button>

                    <AnimatePresence>
                      {activeDropdown === item.name && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: 10 }}
                          transition={{ duration: 0.2 }}
                          className="absolute top-full left-0 mt-2 w-48 bg-[#1E293B] rounded-lg shadow-lg border border-slate-600 py-2 z-50"
                        >
                          {item.dropdown.map((dropdownItem) => (
                            <Link
                              key={dropdownItem.name}
                              href={dropdownItem.href}
                              className="block px-4 py-2 text-slate-200 hover:bg-slate-700/50 hover:text-slate-100 transition-colors duration-200"
                            >
                              {dropdownItem.name}
                            </Link>
                          ))}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                      isScrolled
                        ? "text-slate-200 hover:text-slate-100 hover:bg-slate-700/50"
                        : "text-white/90 hover:text-white hover:bg-white/10"
                    }`}
                  >
                    {item.name}
                  </Link>
                )}
              </motion.div>
            ))}
          </div>



          {/* Search and Mobile Menu */}
          <div className="flex items-center space-x-4">
            {/* Search Button */}
            <button
              onClick={() => setIsSearchOpen(!isSearchOpen)}
              className={`hidden lg:flex p-2 rounded-lg transition-all duration-200 ${
                isScrolled ? "text-slate-200 hover:bg-slate-700/50" : "text-white hover:bg-white/10"
              }`}
            >
              <Search className="h-5 w-5" />
            </button>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className={`lg:hidden p-2 rounded-lg transition-all duration-200 ${
                isScrolled ? "text-slate-200 hover:bg-slate-700/50" : "text-white hover:bg-white/10"
              }`}
            >
              {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Search Overlay */}
      <AnimatePresence>
        {isSearchOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 right-0 bg-white border-b border-slate-200 shadow-lg z-40"
          >
            <div className="container mx-auto px-6 py-6">
              <div className="max-w-xl mx-auto">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                  <input
                    type="text"
                    placeholder="Search..."
                    className="w-full pl-10 pr-4 py-3 bg-slate-50 border border-slate-200 rounded-lg focus:border-blue-500 focus:outline-none transition-colors"
                    autoFocus
                  />
                </div>
                <div className="mt-4 flex flex-wrap gap-2">
                  {[
                    { name: "Healthcare", href: "/businesses/healthcare" },
                    { name: "IT", href: "/businesses/it" },
                    { name: "Energy", href: "/businesses/energy" },
                    { name: "Real Estate", href: "/businesses/real-estate" },
                    { name: "Aviation", href: "/businesses/helicopters" },
                    { name: "Contact", href: "/contact" }
                  ].map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      onClick={() => setIsSearchOpen(false)}
                      className="px-3 py-1 text-sm bg-slate-100 hover:bg-slate-200 rounded-md transition-colors text-slate-700"
                    >
                      {item.name}
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0, y: -20 }}
            animate={{ opacity: 1, height: "auto", y: 0 }}
            exit={{ opacity: 0, height: 0, y: -20 }}
            transition={{ duration: 0.4, ease: "easeOut" }}
            className="lg:hidden relative overflow-hidden"
          >
            {/* Glassmorphism background */}
            <div className="absolute inset-0 bg-[#0B1426]/95 backdrop-blur-xl" />
            <div className="absolute inset-0 bg-gradient-to-br from-[#0B1426]/95 via-[#1E293B]/90 to-[#0B1426]/95" />

            {/* Border */}
            <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-slate-300/30 to-transparent" />

            <div className="container mx-auto px-6 py-8 relative z-10">
              <div className="flex flex-col space-y-2">
                {navItems.map((item, index) => (
                  <motion.div
                    key={item.name}
                    initial={{ opacity: 0, x: -30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1, ease: "easeOut" }}
                  >
                    {item.dropdown ? (
                      <div className="space-y-2">
                        <div className="text-slate-100 font-semibold py-3 px-4 rounded-xl bg-slate-700/50">
                          {item.name}
                        </div>
                        <div className="pl-4 space-y-1">
                          {item.dropdown.map((dropdownItem, dropdownIndex) => (
                            <Link
                              key={dropdownItem.name}
                              href={dropdownItem.href}
                              onClick={() => setIsMobileMenuOpen(false)}
                              className="block text-slate-300 font-medium py-2.5 px-4 hover:text-slate-100 hover:bg-slate-600/50 transition-all duration-200 rounded-lg"
                            >
                              {dropdownItem.name}
                            </Link>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <Link
                        href={item.href}
                        onClick={() => setIsMobileMenuOpen(false)}
                        className="text-slate-200 font-semibold py-3 px-4 hover:text-slate-100 hover:bg-slate-600/50 transition-all duration-200 block rounded-xl"
                      >
                        {item.name}
                      </Link>
                    )}
                  </motion.div>
                ))}


              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.nav>
  );
}
