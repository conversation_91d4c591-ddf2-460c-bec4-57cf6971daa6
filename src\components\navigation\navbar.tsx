"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import { Menu, X, ChevronDown, Search } from "lucide-react";

const navItems = [
  { name: "Home", href: "/" },
  {
    name: "About",
    href: "/about",
    dropdown: [
      { name: "About Us", href: "/about" },
      { name: "Members", href: "/about/members" },
      { name: "Sustainability", href: "/about/sustainability" },
      { name: "Character", href: "/about/character" }
    ]
  },
  {
    name: "Businesses",
    href: "/businesses/healthcare",
    dropdown: [
      { name: "Healthcare", href: "/businesses/healthcare" },
      { name: "IT", href: "/businesses/it" },
      { name: "Energy", href: "/businesses/energy" },
      { name: "Real Estate", href: "/businesses/real-estate" },
      { name: "Helicopters", href: "/businesses/helicopters" },
      { name: "Food", href: "/businesses/food" }
    ]
  },
  { name: "Contact", href: "/contact" },
];

export function Navbar() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <motion.nav
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
        isScrolled
          ? "bg-white/80 backdrop-blur-xl shadow-2xl border-b border-white/20"
          : "bg-gradient-to-b from-black/20 via-black/10 to-transparent backdrop-blur-sm"
      }`}
    >
      {/* Glassmorphism overlay */}
      <div className={`absolute inset-0 transition-all duration-500 ${
        isScrolled
          ? "bg-gradient-to-r from-white/90 via-white/95 to-white/90"
          : "bg-gradient-to-r from-white/5 via-white/10 to-white/5"
      }`} />

      <div className="container mx-auto px-6 relative z-10">
        <div className="flex items-center justify-between h-20 md:h-24">
          {/* Logo */}
          <Link href="/">
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="flex items-center cursor-pointer group"
            >
              <div className="relative">
                <Image
                  src="/images/logo-klever.png"
                  alt="KleverCo Logo"
                  width={200}
                  height={80}
                  className="h-16 md:h-20 w-auto transition-all duration-300 group-hover:brightness-110"
                />
                {/* Premium glow effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/30 to-purple-500/30 rounded-xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10" />
              </div>
            </motion.div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-2">
            {navItems.map((item, index) => (
              <motion.div
                key={item.name}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1, ease: "easeOut" }}
                className="relative"
                onMouseEnter={() => item.dropdown && setActiveDropdown(item.name)}
                onMouseLeave={() => setActiveDropdown(null)}
              >
                {item.dropdown ? (
                  <div className="relative">
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className={`relative px-6 py-3 rounded-xl font-medium transition-all duration-300 flex items-center group ${
                        isScrolled
                          ? "text-slate-700 hover:text-slate-900 hover:bg-slate-100/80"
                          : "text-white/90 hover:text-white hover:bg-white/10"
                      }`}
                    >
                      <span className="relative z-10 text-lg">{item.name}</span>
                      <ChevronDown className={`h-4 w-4 ml-2 transition-transform duration-300 ${
                        activeDropdown === item.name ? "rotate-180" : ""
                      }`} />

                      {/* Premium background glow */}
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </motion.button>

                    <AnimatePresence>
                      {activeDropdown === item.name && (
                        <motion.div
                          initial={{ opacity: 0, y: 10, scale: 0.95 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          exit={{ opacity: 0, y: 10, scale: 0.95 }}
                          transition={{ duration: 0.3, ease: "easeOut" }}
                          className="absolute top-full left-0 mt-3 w-56 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 py-3 z-50 overflow-hidden"
                        >
                          {/* Glassmorphism background */}
                          <div className="absolute inset-0 bg-gradient-to-br from-white/90 via-white/95 to-white/90" />

                          {item.dropdown.map((dropdownItem, dropdownIndex) => (
                            <motion.div
                              key={dropdownItem.name}
                              initial={{ opacity: 0, x: -10 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ duration: 0.2, delay: dropdownIndex * 0.05 }}
                              className="relative"
                            >
                              <Link
                                href={dropdownItem.href}
                                className="relative block px-5 py-3 text-slate-700 hover:text-slate-900 transition-all duration-200 group"
                              >
                                <span className="font-medium">{dropdownItem.name}</span>

                                {/* Hover background */}
                                <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-purple-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg mx-2" />
                              </Link>
                            </motion.div>
                          ))}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                ) : (
                  <Link href={item.href}>
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className={`relative px-6 py-3 rounded-xl font-medium transition-all duration-300 group ${
                        isScrolled
                          ? "text-slate-700 hover:text-slate-900 hover:bg-slate-100/80"
                          : "text-white/90 hover:text-white hover:bg-white/10"
                      }`}
                    >
                      <span className="relative z-10 text-lg">{item.name}</span>

                      {/* Premium background glow */}
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </motion.div>
                  </Link>
                )}
              </motion.div>
            ))}
          </div>



          {/* Search and Mobile Menu */}
          <div className="flex items-center space-x-4">
            {/* Search Button */}
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setIsSearchOpen(!isSearchOpen)}
              className={`hidden lg:flex p-3 rounded-xl transition-all duration-300 relative group ${
                isScrolled ? "text-slate-700 hover:bg-slate-100/80" : "text-white hover:bg-white/10"
              }`}
            >
              <Search className="h-5 w-5" />
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </motion.button>

            {/* Mobile Menu Button */}
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className={`lg:hidden p-3 rounded-xl transition-all duration-300 relative group ${
                isScrolled ? "text-slate-700 hover:bg-slate-100/80" : "text-white hover:bg-white/10"
              }`}
            >
              <div className="relative z-10">
                {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </div>

              {/* Premium background glow */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </motion.button>
          </div>
        </div>
      </div>

      {/* Search Overlay */}
      <AnimatePresence>
        {isSearchOpen && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="absolute top-full left-0 right-0 bg-white/95 backdrop-blur-xl border-b border-slate-200/50 shadow-2xl z-40"
          >
            <div className="container mx-auto px-6 py-8">
              <div className="max-w-2xl mx-auto">
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-6 w-6 text-slate-400" />
                  <input
                    type="text"
                    placeholder="Search businesses, services, or information..."
                    className="w-full pl-12 pr-4 py-4 text-lg bg-white border-2 border-slate-200 rounded-2xl focus:border-blue-500 focus:outline-none transition-colors"
                    autoFocus
                  />
                </div>
                <div className="mt-6 grid grid-cols-2 md:grid-cols-3 gap-4">
                  {["Healthcare", "IT Solutions", "Energy", "Real Estate", "Aviation", "Contact"].map((item) => (
                    <button
                      key={item}
                      className="text-left p-3 rounded-xl hover:bg-slate-100 transition-colors text-slate-700 font-medium"
                    >
                      {item}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0, y: -20 }}
            animate={{ opacity: 1, height: "auto", y: 0 }}
            exit={{ opacity: 0, height: 0, y: -20 }}
            transition={{ duration: 0.4, ease: "easeOut" }}
            className="lg:hidden relative overflow-hidden"
          >
            {/* Glassmorphism background */}
            <div className="absolute inset-0 bg-white/90 backdrop-blur-xl" />
            <div className="absolute inset-0 bg-gradient-to-br from-white/95 via-white/90 to-white/95" />

            {/* Border */}
            <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-slate-300/50 to-transparent" />

            <div className="container mx-auto px-6 py-8 relative z-10">
              <div className="flex flex-col space-y-2">
                {navItems.map((item, index) => (
                  <motion.div
                    key={item.name}
                    initial={{ opacity: 0, x: -30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1, ease: "easeOut" }}
                  >
                    {item.dropdown ? (
                      <div className="space-y-2">
                        <div className="text-slate-900 font-semibold py-3 px-4 rounded-xl bg-slate-50/80">
                          {item.name}
                        </div>
                        <div className="pl-4 space-y-1">
                          {item.dropdown.map((dropdownItem, dropdownIndex) => (
                            <Link
                              key={dropdownItem.name}
                              href={dropdownItem.href}
                              onClick={() => setIsMobileMenuOpen(false)}
                              className="block text-slate-600 font-medium py-2.5 px-4 hover:text-slate-900 hover:bg-slate-100/60 transition-all duration-200 rounded-lg"
                            >
                              {dropdownItem.name}
                            </Link>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <Link
                        href={item.href}
                        onClick={() => setIsMobileMenuOpen(false)}
                        className="text-slate-700 font-semibold py-3 px-4 hover:text-slate-900 hover:bg-slate-100/60 transition-all duration-200 block rounded-xl"
                      >
                        {item.name}
                      </Link>
                    )}
                  </motion.div>
                ))}


              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.nav>
  );
}
