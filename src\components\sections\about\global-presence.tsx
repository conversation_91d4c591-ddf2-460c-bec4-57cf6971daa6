"use client";

import { motion } from "framer-motion";
import { MapPin, Phone, Mail } from "lucide-react";

const offices = [
  {
    name: "KLEVER Global",
    country: "United States",
    address: "30N Gould Street, ste R, Sheridan, WY 82801, USA",
    phone: "+****************",
    type: "Global Headquarters",
    color: "from-blue-500 to-indigo-500"
  },
  {
    name: "KLEVER SDN. BHD.",
    country: "Malaysia", 
    address: "21-9 Office Suites, 1 Mont' Kiara, No 1 Jalan Kiara, Mont Kiara, 50480 Kuala Lumpur, Malaysia",
    phone: "+60 3 6201 5195",
    type: "Regional Operations",
    color: "from-green-500 to-emerald-500"
  },
  {
    name: "KLEVER Thai Hua Gloves",
    country: "Thailand",
    address: "238/10 Ratchada-Pisek Road, Huai-Khwang, Bangkok 10310, Thailand",
    phone: "",
    type: "Manufacturing Hub",
    color: "from-red-500 to-pink-500"
  }
];

export function GlobalPresence() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
            Global <span className="text-teal-500">Presence</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            A global investment and research boutique firm with strategic locations across three continents, 
            ensuring comprehensive coverage and local expertise in key markets.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
          {offices.map((office, index) => (
            <motion.div
              key={office.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-white rounded-3xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 h-full">
                {/* Header */}
                <div className="text-center mb-6">
                  <div className={`w-16 h-16 bg-gradient-to-r ${office.color} rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <MapPin className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-slate-900 group-hover:text-teal-600 transition-colors duration-300 mb-2">
                    {office.name}
                  </h3>
                  <p className="text-teal-600 font-semibold">{office.type}</p>
                </div>

                {/* Country */}
                <div className="mb-4">
                  <div className="text-lg font-bold text-slate-800 mb-2">{office.country}</div>
                </div>

                {/* Address */}
                <div className="mb-6">
                  <div className="flex items-start mb-3">
                    <MapPin className="h-5 w-5 text-slate-400 mr-3 mt-1 flex-shrink-0" />
                    <p className="text-slate-600 leading-relaxed">{office.address}</p>
                  </div>
                  
                  {office.phone && (
                    <div className="flex items-center">
                      <Phone className="h-5 w-5 text-slate-400 mr-3" />
                      <a href={`tel:${office.phone}`} className="text-slate-600 hover:text-teal-600 transition-colors duration-300">
                        {office.phone}
                      </a>
                    </div>
                  )}
                </div>

                {/* CTA */}
                <button className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 bg-gradient-to-r ${office.color} text-white hover:shadow-lg hover:scale-105`}>
                  Contact Office
                </button>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Contact Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="bg-gradient-to-r from-slate-50 to-blue-50 rounded-3xl p-12 text-center"
        >
          <Mail className="h-16 w-16 text-teal-500 mx-auto mb-6" />
          <h3 className="text-3xl md:text-4xl font-bold text-slate-900 mb-6">
            Get in Touch
          </h3>
          <p className="text-xl text-slate-700 mb-8 max-w-2xl mx-auto">
            Ready to explore investment opportunities or learn more about our services? 
            Contact our team for personalized consultation.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a 
              href="mailto:<EMAIL>"
              className="bg-teal-500 hover:bg-teal-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300 shadow-lg hover:shadow-xl"
            >
              <EMAIL>
            </a>
            <button className="border-2 border-teal-500 text-teal-600 hover:bg-teal-500 hover:text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300">
              Schedule Consultation
            </button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
