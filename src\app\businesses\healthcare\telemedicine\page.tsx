import { Navbar } from "@/components/navigation/navbar";
import { TelemedicineHero } from "@/components/sections/businesses/healthcare/telemedicine-hero";
import { HealthcareServices } from "@/components/sections/businesses/healthcare/healthcare-services";
import { Footer } from "@/components/navigation/footer";

export const metadata = {
  title: "Telemedicine - KleverCo Healthcare",
  description: "Healthcare services provided remotely using telecommunications technology, enabling healthcare professionals to evaluate, diagnose, and treat patients without in-person visits.",
};

export default function TelemedicinePage() {
  return (
    <main className="min-h-screen bg-white">
      <Navbar />
      <TelemedicineHero />
      <HealthcareServices />
      <Footer />
    </main>
  );
}
