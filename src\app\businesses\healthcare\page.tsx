import { Navbar } from "@/components/navigation/navbar";
import { HealthcareHero } from "@/components/sections/businesses/healthcare/healthcare-hero";
import { HealthcareServices } from "@/components/sections/businesses/healthcare/healthcare-services";
import { HIMS } from "@/components/sections/businesses/healthcare/hims";
import { WHOA } from "@/components/sections/businesses/healthcare/whoa";
import { Dentistry } from "@/components/sections/businesses/healthcare/dentistry";
import { Gloves } from "@/components/sections/businesses/healthcare/gloves";
import { Footer } from "@/components/navigation/footer";

export const metadata = {
  title: "Healthcare - KleverCo Global Investment & Research Boutique",
  description: "KleverCo's Healthcare division covering Telemedicine, HIMS, GLOVES, WHOA, and Dentistry services with comprehensive solutions.",
};

export default function HealthcarePage() {
  return (
    <main className="min-h-screen bg-white">
      <Navbar />
      <HealthcareHero />
      <HealthcareServices />
      <HIMS />
      <WHOA />
      <Dentistry />
      <Gloves />
      <Footer />
    </main>
  );
}
