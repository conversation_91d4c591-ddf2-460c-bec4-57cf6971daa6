"use client";

import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { Mail, Phone, MapPin, Linkedin, Twitter, Facebook, Instagram } from "lucide-react";

const footerSections = [
  {
    title: "About",
    links: [
      { name: "About Us", href: "/about" },
      { name: "Our Team", href: "/about/members" },
      { name: "Sustainability", href: "/about/sustainability" },
      { name: "Character", href: "/about/character" },
    ]
  },
  {
    title: "Businesses",
    links: [
      { name: "Healthcare", href: "/businesses/healthcare" },
      { name: "Information Technology", href: "/businesses/it" },
      { name: "Energy Solutions", href: "/businesses/energy" },
      { name: "Real Estate", href: "/businesses/real-estate" },
      { name: "Aviation", href: "/businesses/helicopters" },
      { name: "Food & Beverage", href: "/businesses/food" },
    ]
  },
  {
    title: "Global Offices",
    links: [
      { name: "USA Office", href: "/contact#usa" },
      { name: "Malaysia Office", href: "/contact#malaysia" },
      { name: "Thailand Office", href: "/contact#thailand" },
      { name: "Contact Us", href: "/contact" },
    ]
  }
];

const socialLinks = [
  { icon: Linkedin, href: "#", label: "LinkedIn" },
  { icon: Twitter, href: "#", label: "Twitter" },
  { icon: Instagram, href: "#", label: "Instagram" },
  { icon: Facebook, href: "#", label: "Facebook" },
];

export function Footer() {
  return (
    <footer className="relative bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%22100%22%20height%3D%22100%22%20viewBox%3D%220%200%20100%20100%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.02%22%3E%3Cpath%20d%3D%22M50%2050L0%200h100L50%2050zM50%2050L100%20100H0L50%2050z%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div>

      {/* Glassmorphism overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-slate-900/50 to-transparent backdrop-blur-[1px]"></div>

      <div className="container mx-auto px-6 py-20 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-12">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="mb-8"
            >
              <div className="flex items-center mb-6">
                <Image
                  src="/images/logo-klever.png"
                  alt="KleverCo Logo"
                  width={180}
                  height={72}
                  className="h-18 w-auto"
                />
              </div>
              <p className="text-slate-300 leading-relaxed mb-8 text-lg">
                A global investment and research boutique firm delivering comprehensive
                business solutions across healthcare, technology, energy, real estate,
                aviation, and food sectors.
              </p>
            </motion.div>

            {/* Contact Info */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="space-y-4"
            >
              <div className="flex items-start space-x-4 text-slate-300">
                <MapPin className="h-5 w-5 text-teal-400 mt-1 flex-shrink-0" />
                <div>
                  <p className="font-medium text-white mb-1">Global Headquarters</p>
                  <p>30N Gould Street, Sheridan, WY 82801, USA</p>
                </div>
              </div>
              <div className="flex items-center space-x-4 text-slate-300">
                <Mail className="h-5 w-5 text-teal-400 flex-shrink-0" />
                <div>
                  <p className="font-medium text-white mb-1">Email</p>
                  <Link href="mailto:<EMAIL>" className="hover:text-teal-400 transition-colors">
                    <EMAIL>
                  </Link>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Footer Links */}
          {footerSections.map((section, index) => (
            <motion.div
              key={section.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <h3 className="text-xl font-semibold text-white relative">
                {section.title}
                <div className="absolute -bottom-2 left-0 w-8 h-0.5 bg-gradient-to-r from-teal-400 to-blue-400 rounded-full"></div>
              </h3>
              <ul className="space-y-4">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-slate-300 hover:text-teal-400 transition-all duration-300 hover:translate-x-1 inline-block font-medium"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

  

        {/* Bottom Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="border-t border-slate-700/50 pt-8 mt-8"
        >
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-6 lg:space-y-0">
            {/* Copyright */}
            <div className="text-slate-400 text-center lg:text-left">
              <p className="text-lg font-medium">© 2025 Kleverco - All rights reserved.</p>
              <p className="text-sm mt-1">A Global Investment and Research Boutique Firm</p>
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-6">
              {socialLinks.map((social, index) => {
                const Icon = social.icon;
                return (
                  <motion.a
                    key={social.label}
                    href={social.href}
                    initial={{ opacity: 0, scale: 0 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: 0.7 + index * 0.1 }}
                    viewport={{ once: true }}
                    whileHover={{ scale: 1.2, y: -2 }}
                    className="w-12 h-12 bg-gradient-to-br from-slate-800 to-slate-700 rounded-xl flex items-center justify-center text-slate-400 hover:text-white hover:from-teal-500 hover:to-blue-500 transition-all duration-300 shadow-lg hover:shadow-xl"
                    aria-label={social.label}
                  >
                    <Icon className="h-5 w-5" />
                  </motion.a>
                );
              })}
            </div>

            {/* Legal Links */}
            <div className="flex items-center space-x-8 text-slate-400">
              <Link href="/privacy" className="hover:text-teal-400 transition-colors duration-300 font-medium">
                Privacy Policy
              </Link>
              <Link href="/terms" className="hover:text-teal-400 transition-colors duration-300 font-medium">
                Terms of Service
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
}
