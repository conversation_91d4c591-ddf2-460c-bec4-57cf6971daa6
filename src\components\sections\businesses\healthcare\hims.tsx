"use client";

import { motion } from "framer-motion";
import { Database, Globe, Shield, Building, Users, Stethoscope } from "lucide-react";

export function HIMS() {
  const projects = [
    "Computerization of Lahore General Hospital",
    "Computerization of Shaikh <PERSON> Hospital", 
    "Web Portal of Punjab Education Sector Reform Program (PESRP)",
    "Computerization of Trading Corporation of Pakistan",
    "Digital Library - Ministry of Commerce",
    "Computerization of OMAR Hospital Lahore",
    "MOIT - HEALTH NET Telemedicine Project for Rural/Remote Areas of Pakistan",
    "Access to Statutory/Caselaws at District Bar Associations",
    "Law Department Management Information System",
    "Computerization of Katchi Abadies",
    "Higher Education Information Management System",
    "PROMIS & AFIS SERS - Smart Emergency Response System"
  ];

  const features = [
    {
      icon: Database,
      title: "Comprehensive Integration",
      description: "Complete ERP solution covering all hospital necessities with seamless data management"
    },
    {
      icon: Globe,
      title: "International Standards",
      description: "Incorporates Health Level 7 (HL7) for worldwide healthcare data exchange compliance"
    },
    {
      icon: Shield,
      title: "Enhanced Security",
      description: "Powerful data protection with flexible interface adaptable to users with minimal system knowledge"
    },
    {
      icon: Stethoscope,
      title: "Telemedicine Integration",
      description: "Live communication solutions for Tele-Diagnosis, Consultation, and Treatment"
    },
    {
      icon: Building,
      title: "Network Architecture",
      description: "Establishes network of remote hospitals, hub hospitals, and central server control"
    },
    {
      icon: Users,
      title: "User-Friendly Design",
      description: "Swift deployment with smooth operation providing speedy and updated user experience"
    }
  ];

  return (
    <section className="py-32 bg-slate-50">
      <div className="container mx-auto px-6">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-8 tracking-tight">
              HIMS
            </h2>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto mb-8"></div>
            <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed font-light">
              Hospital Information Management System - Complete ERP solution for healthcare automation
            </p>
          </motion.div>

          {/* Sparrow EMR Introduction */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-20"
          >
            <div className="bg-white rounded-2xl p-12 shadow-lg border border-slate-100">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-3xl font-light text-slate-900 mb-6 tracking-tight">
                    Sparrow EMR
                  </h3>
                  <div className="w-16 h-px bg-slate-300 mb-6"></div>
                  <p className="text-lg text-slate-700 leading-relaxed font-light mb-6">
                    We developed a healthcare product "Sparrow EMR" which is a complete ERP solution. Sparrow EMR is a web-based healthcare application covering the necessities of hospitals in Pakistan and allowing them to communicate clinical data at National and International level.
                  </p>
                  <p className="text-slate-600 leading-relaxed font-light">
                    Our application incorporates Health Level 7 that provides standards for the exchange of data among worldwide healthcare computing applications. Several other standards are also used in conjunction with HL7 such as ICD-10 disease codes, ICD-10 procedure codes, LOINC, DICOM.
                  </p>
                </div>
                
                <div className="bg-slate-50 rounded-xl p-8">
                  <h4 className="text-xl font-medium text-slate-900 mb-4">Key Standards</h4>
                  <div className="space-y-3">
                    {["Health Level 7 (HL7)", "ICD-10 Disease Codes", "ICD-10 Procedure Codes", "LOINC", "DICOM"].map((standard, index) => (
                      <div key={standard} className="flex items-center">
                        <div className="w-2 h-2 bg-slate-900 rounded-full mr-3"></div>
                        <span className="text-slate-700 font-light">{standard}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Healthcare Sector Automation */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="mb-20"
          >
            <div className="text-center mb-16">
              <h3 className="text-3xl font-light text-slate-900 mb-8 tracking-tight">
                Healthcare Sector <span className="font-normal text-slate-600">Automation</span>
              </h3>
            </div>

            <div className="bg-white rounded-2xl p-12 shadow-lg border border-slate-100 mb-12">
              <p className="text-lg text-slate-700 leading-relaxed font-light mb-8">
                Automation over the years had been used and treated as just a buzz and its mere presence was in some form of auxiliary functions. Now with changing times automation solutions are being basis of business model and used as a means for efficiency gains, facilitation for patients and a tool for decision making.
              </p>
              <p className="text-slate-600 leading-relaxed font-light">
                Oratier Technologies is the leader in initiating IT conversion of processes. We are the pioneers in automating Government Hospitals all over Punjab in accordance to International health standards HL7. Oratier started from City of Lahore at General Hospital and Mayo Hospital the main biggest Tertiary Care Facilities and then implemented phase wise to all hospitals of the Government of Punjab, Pakistan.
              </p>
            </div>

            {/* Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => {
                const Icon = feature.icon;
                
                return (
                  <motion.div
                    key={feature.title}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="bg-white rounded-xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300"
                  >
                    <div className="bg-slate-900 p-3 rounded-xl w-fit mb-6">
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                    <h4 className="text-xl font-medium text-slate-900 mb-4">
                      {feature.title}
                    </h4>
                    <p className="text-slate-600 font-light leading-relaxed">
                      {feature.description}
                    </p>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* HACIMS Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
            className="mb-20"
          >
            <div className="bg-slate-900 rounded-2xl p-12 text-white">
              <h3 className="text-3xl font-light mb-6 tracking-tight">
                HACIMS
              </h3>
              <p className="text-slate-300 text-lg mb-6 font-light">
                Hospital Automation & Clinical Information Management System
              </p>
              <div className="w-16 h-px bg-slate-400 mb-8"></div>
              <p className="text-slate-300 leading-relaxed font-light text-lg">
                HACIMS is a comprehensive, integrated Hospital Management Information System designed to coherently manage all aspects of small to large scale hospitals' end-to-end automation. Our Solution, HACIMS is powerful & easy to use with enhanced data protection & security. Its interface is flexible enough to adapt to users with minimal system knowledge. It deploys swiftly and runs smoothly to provide the best, speedy and most updated experience.
              </p>
            </div>
          </motion.div>

          {/* Projects Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <div className="text-center mb-16">
              <h3 className="text-3xl font-light text-slate-900 mb-8 tracking-tight">
                Our <span className="font-normal text-slate-600">Projects</span>
              </h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {projects.map((project, index) => (
                <motion.div
                  key={project}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.05 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-xl p-6 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300"
                >
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-slate-900 rounded-full mt-3 mr-4 flex-shrink-0"></div>
                    <p className="text-slate-700 font-light leading-relaxed">
                      {project}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
