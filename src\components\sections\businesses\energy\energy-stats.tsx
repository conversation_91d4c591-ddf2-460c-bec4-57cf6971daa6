"use client";

import { motion } from "framer-motion";
import { TrendingUp, Truck, Users, Zap } from "lucide-react";

export function EnergyStats() {
  const hydrocarbonStats = [
    {
      value: "35,000 MT",
      label: "LPG STORAGE",
      icon: TrendingUp
    },
    {
      value: "25+",
      label: "Tanker LPG DELIVERED ANNUALLY",
      icon: Truck
    },
    {
      value: "1+ million MT",
      label: "LPG TRADED ANNUALLY",
      icon: TrendingUp
    },
    {
      value: "1000+",
      label: "Trucks MOVING LPG MONTHLY",
      icon: Truck
    },
    {
      value: "1+ million",
      label: "CUSTOMERS SERVED MONTHLY",
      icon: Users
    },
    {
      value: "7 million",
      label: "TOE LNG TRADED/CONTRACTED",
      icon: TrendingUp
    },
    {
      value: "12+ million",
      label: "BBL CRUDE TRADED ANNUALLY",
      icon: TrendingUp
    },
    {
      value: "20+",
      label: "Cargos CRUDE MOVED ANNUALLY",
      icon: Truck
    }
  ];

  const renewableStats = [
    {
      value: "1000+",
      label: "<PERSON><PERSON><PERSON> POWERED GEYSERS DELIVERED",
      icon: Zap
    },
    {
      value: "2500+",
      label: "Solar panels Delivered MONTHLY",
      icon: Zap
    },
    {
      value: "50+",
      label: "EV CHARGING POWER STATIONS Annually",
      icon: Zap
    },
    {
      value: "200+",
      label: "EV CHARGING UNITS Annually",
      icon: Zap
    },
    {
      value: "100,000+",
      label: "CUSTOMERS SERVED ANNUALLY",
      icon: Users
    }
  ];

  return (
    <section className="py-32 bg-white">
      <div className="container mx-auto px-6">
        <div className="max-w-7xl mx-auto">
          {/* Hydrocarbons Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-20"
          >
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-8 tracking-tight">
                Hydrocarbons at a <span className="font-normal text-slate-600">Glance</span>
              </h2>
              <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {hydrocarbonStats.map((stat, index) => {
                const Icon = stat.icon;
                
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="bg-slate-50 rounded-2xl p-8 text-center border border-slate-100 hover:shadow-lg transition-all duration-300"
                  >
                    <div className="bg-slate-900 p-4 rounded-2xl w-fit mx-auto mb-6">
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <div className="text-3xl md:text-4xl font-light text-slate-900 mb-3 tracking-tight">
                      {stat.value}
                    </div>
                    <div className="text-sm font-medium text-slate-600 uppercase tracking-wider">
                      {stat.label}
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Renewable Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-8 tracking-tight">
                Renewable at a <span className="font-normal text-slate-600">Glance</span>
              </h2>
              <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {renewableStats.map((stat, index) => {
                const Icon = stat.icon;
                
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="bg-slate-50 rounded-2xl p-8 text-center border border-slate-100 hover:shadow-lg transition-all duration-300"
                  >
                    <div className="bg-slate-900 p-4 rounded-2xl w-fit mx-auto mb-6">
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <div className="text-3xl md:text-4xl font-light text-slate-900 mb-3 tracking-tight">
                      {stat.value}
                    </div>
                    <div className="text-sm font-medium text-slate-600 uppercase tracking-wider">
                      {stat.label}
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
