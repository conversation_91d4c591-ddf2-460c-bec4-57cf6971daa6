{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/navigation/navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { Button } from \"@/components/ui/button\";\nimport { TrendingUp, Menu, X } from \"lucide-react\";\n\nconst navItems = [\n  { name: \"Home\", href: \"/\" },\n  { name: \"About\", href: \"/about\" },\n  { name: \"Businesses\", href: \"/businesses\" },\n  { name: \"Contact\", href: \"/contact\" },\n];\n\nexport function Navbar() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  return (\n    <motion.nav\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.8 }}\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        isScrolled \n          ? \"bg-white/95 backdrop-blur-md shadow-lg border-b border-slate-200/50\" \n          : \"bg-transparent\"\n      }`}\n    >\n      <div className=\"container mx-auto px-6\">\n        <div className=\"flex items-center justify-between h-16 md:h-20\">\n          {/* Logo */}\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            className=\"flex items-center space-x-2 cursor-pointer\"\n          >\n            <TrendingUp className={`h-8 w-8 ${isScrolled ? \"text-teal-600\" : \"text-teal-400\"}`} />\n            <span className={`text-2xl font-bold ${isScrolled ? \"text-slate-900\" : \"text-white\"}`}>\n              Klever<span className={isScrolled ? \"text-teal-600\" : \"text-teal-400\"}>Co</span>\n            </span>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item, index) => (\n              <motion.div\n                key={item.name}\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n              >\n                <Link\n                  href={item.href}\n                  className={`font-medium transition-colors duration-300 hover:text-teal-500 ${\n                    isScrolled ? \"text-slate-700\" : \"text-white\"\n                  }`}\n                >\n                  {item.name}\n                </Link>\n              </motion.div>\n            ))}\n          </div>\n\n          {/* Desktop CTA */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button\n              variant={isScrolled ? \"outline\" : \"secondary\"}\n              className={`transition-all duration-300 ${\n                isScrolled \n                  ? \"border-teal-600 text-teal-600 hover:bg-teal-600 hover:text-white\" \n                  : \"bg-white/20 text-white border-white/30 hover:bg-white hover:text-slate-900\"\n              }`}\n            >\n              Get Started\n            </Button>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className={`md:hidden p-2 rounded-lg transition-colors duration-300 ${\n              isScrolled ? \"text-slate-700 hover:bg-slate-100\" : \"text-white hover:bg-white/20\"\n            }`}\n          >\n            {isMobileMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      <AnimatePresence>\n        {isMobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"md:hidden bg-white/95 backdrop-blur-md border-t border-slate-200/50\"\n          >\n            <div className=\"container mx-auto px-6 py-4\">\n              <div className=\"flex flex-col space-y-4\">\n                {navItems.map((item, index) => (\n                  <motion.div\n                    key={item.name}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ duration: 0.3, delay: index * 0.1 }}\n                  >\n                    <Link\n                      href={item.href}\n                      onClick={() => setIsMobileMenuOpen(false)}\n                      className=\"text-slate-700 font-medium py-2 hover:text-teal-600 transition-colors duration-300 block\"\n                    >\n                      {item.name}\n                    </Link>\n                  </motion.div>\n                ))}\n                <motion.div\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.3, delay: navItems.length * 0.1 }}\n                  className=\"pt-4 border-t border-slate-200\"\n                >\n                  <Button className=\"w-full bg-teal-600 hover:bg-teal-700 text-white\">\n                    Get Started\n                  </Button>\n                </motion.div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AANA;;;;;;;AAQA,MAAM,WAAW;IACf;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAc,MAAM;IAAc;IAC1C;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAC,4DAA4D,EACtE,aACI,wEACA,kBACJ;;0BAEF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,WAAU;;8CAEV,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAW,CAAC,QAAQ,EAAE,aAAa,kBAAkB,iBAAiB;;;;;;8CAClF,8OAAC;oCAAK,WAAW,CAAC,mBAAmB,EAAE,aAAa,mBAAmB,cAAc;;wCAAE;sDAC/E,8OAAC;4CAAK,WAAW,aAAa,kBAAkB;sDAAiB;;;;;;;;;;;;;;;;;;sCAK3E,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;8CAEhD,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,+DAA+D,EACzE,aAAa,mBAAmB,cAChC;kDAED,KAAK,IAAI;;;;;;mCAXP,KAAK,IAAI;;;;;;;;;;sCAkBpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,aAAa,YAAY;gCAClC,WAAW,CAAC,4BAA4B,EACtC,aACI,qEACA,8EACJ;0CACH;;;;;;;;;;;sCAMH,8OAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAW,CAAC,wDAAwD,EAClE,aAAa,sCAAsC,gCACnD;sCAED,iCAAmB,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMtE,8OAAC,yLAAA,CAAA,kBAAe;0BACb,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;kDAEhD,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,oBAAoB;4CACnC,WAAU;sDAET,KAAK,IAAI;;;;;;uCAVP,KAAK,IAAI;;;;;8CAclB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,SAAS,MAAM,GAAG;oCAAI;oCAC1D,WAAU;8CAEV,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;kDAAkD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtF", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/navigation/footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { TrendingUp, Mail, Phone, MapPin, Linkedin, Twitter, Facebook } from \"lucide-react\";\n\nconst footerSections = [\n  {\n    title: \"Company\",\n    links: [\n      { name: \"About Us\", href: \"#about\" },\n      { name: \"Our Team\", href: \"#team\" },\n      { name: \"Careers\", href: \"#careers\" },\n      { name: \"News\", href: \"#news\" },\n    ]\n  },\n  {\n    title: \"Investment\",\n    links: [\n      { name: \"Sectors\", href: \"#sectors\" },\n      { name: \"Portfolio\", href: \"#portfolio\" },\n      { name: \"Process\", href: \"#process\" },\n      { name: \"ESG\", href: \"#esg\" },\n    ]\n  },\n  {\n    title: \"Resources\",\n    links: [\n      { name: \"Research\", href: \"#research\" },\n      { name: \"Reports\", href: \"#reports\" },\n      { name: \"Insights\", href: \"#insights\" },\n      { name: \"Events\", href: \"#events\" },\n    ]\n  }\n];\n\nconst socialLinks = [\n  { icon: Linkedin, href: \"#\", label: \"LinkedIn\" },\n  { icon: Twitter, href: \"#\", label: \"Twitter\" },\n  { icon: Facebook, href: \"#\", label: \"Facebook\" },\n];\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-slate-900 text-white\">\n      <div className=\"container mx-auto px-6 py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"mb-6\"\n            >\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <TrendingUp className=\"h-8 w-8 text-teal-400\" />\n                <span className=\"text-2xl font-bold\">\n                  Klever<span className=\"text-teal-400\">Co</span>\n                </span>\n              </div>\n              <p className=\"text-slate-300 leading-relaxed mb-6\">\n                Leading investment company specializing in strategic partnerships across \n                Healthcare, Technology, Energy, and Agriculture sectors. Building tomorrow's \n                success stories today.\n              </p>\n            </motion.div>\n\n            {/* Contact Info */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.1 }}\n              viewport={{ once: true }}\n              className=\"space-y-3\"\n            >\n              <div className=\"flex items-center space-x-3 text-slate-300\">\n                <MapPin className=\"h-5 w-5 text-teal-400\" />\n                <span>123 Investment Plaza, Financial District, NY 10004</span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-slate-300\">\n                <Phone className=\"h-5 w-5 text-teal-400\" />\n                <span>+****************</span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-slate-300\">\n                <Mail className=\"h-5 w-5 text-teal-400\" />\n                <span><EMAIL></span>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Footer Links */}\n          {footerSections.map((section, index) => (\n            <motion.div\n              key={section.title}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"text-lg font-semibold mb-4 text-white\">\n                {section.title}\n              </h3>\n              <ul className=\"space-y-3\">\n                {section.links.map((link) => (\n                  <li key={link.name}>\n                    <a\n                      href={link.href}\n                      className=\"text-slate-300 hover:text-teal-400 transition-colors duration-300\"\n                    >\n                      {link.name}\n                    </a>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.5 }}\n          viewport={{ once: true }}\n          className=\"border-t border-slate-700 mt-12 pt-8\"\n        >\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            {/* Copyright */}\n            <div className=\"text-slate-400 text-sm\">\n              © 2024 KleverCo Investment Company. All rights reserved.\n            </div>\n\n            {/* Social Links */}\n            <div className=\"flex items-center space-x-4\">\n              {socialLinks.map((social, index) => {\n                const Icon = social.icon;\n                return (\n                  <motion.a\n                    key={social.label}\n                    href={social.href}\n                    initial={{ opacity: 0, scale: 0 }}\n                    whileInView={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.3, delay: 0.6 + index * 0.1 }}\n                    viewport={{ once: true }}\n                    whileHover={{ scale: 1.1 }}\n                    className=\"w-10 h-10 bg-slate-800 rounded-full flex items-center justify-center text-slate-400 hover:text-teal-400 hover:bg-slate-700 transition-all duration-300\"\n                    aria-label={social.label}\n                  >\n                    <Icon className=\"h-5 w-5\" />\n                  </motion.a>\n                );\n              })}\n            </div>\n\n            {/* Legal Links */}\n            <div className=\"flex items-center space-x-6 text-sm text-slate-400\">\n              <a href=\"#privacy\" className=\"hover:text-teal-400 transition-colors duration-300\">\n                Privacy Policy\n              </a>\n              <a href=\"#terms\" className=\"hover:text-teal-400 transition-colors duration-300\">\n                Terms of Service\n              </a>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,iBAAiB;IACrB;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;YAAS;YACnC;gBAAE,MAAM;gBAAY,MAAM;YAAQ;YAClC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;SAC/B;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAO,MAAM;YAAO;SAC7B;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAU,MAAM;YAAU;SACnC;IACH;CACD;AAED,MAAM,cAAc;IAClB;QAAE,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAK,OAAO;IAAW;IAC/C;QAAE,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAK,OAAO;IAAU;IAC7C;QAAE,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAK,OAAO;IAAW;CAChD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;oDAAK,WAAU;;wDAAqB;sEAC7B,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAG1C,8OAAC;4CAAE,WAAU;sDAAsC;;;;;;;;;;;;8CAQrD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;wBAMX,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,MAAM,QAAQ;gCAAI;gCACtD,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK;;;;;;kDAEhB,8OAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC;0DACC,cAAA,8OAAC;oDACC,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;+BAXjB,QAAQ,KAAK;;;;;;;;;;;8BA0BxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CAAyB;;;;;;0CAKxC,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;oCACxB,MAAM,OAAO,OAAO,IAAI;oCACxB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCAEP,MAAM,OAAO,IAAI;wCACjB,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM,QAAQ;wCAAI;wCACtD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CAAE,OAAO;wCAAI;wCACzB,WAAU;wCACV,cAAY,OAAO,KAAK;kDAExB,cAAA,8OAAC;4CAAK,WAAU;;;;;;uCAVX,OAAO,KAAK;;;;;gCAavB;;;;;;0CAIF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAqD;;;;;;kDAGlF,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAAqD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9F", "debugId": null}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/sections/contact/contact-hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Mail, Phone, MapPin } from \"lucide-react\";\n\nexport function ContactHero() {\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-blue-900 to-teal-900 overflow-hidden pt-20\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.05%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20\"></div>\n      \n      {/* Floating Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <motion.div\n          animate={{ \n            y: [0, -20, 0],\n            rotate: [0, 5, 0]\n          }}\n          transition={{ \n            duration: 6,\n            repeat: Infinity,\n            ease: \"easeInOut\"\n          }}\n          className=\"absolute top-20 left-10 w-20 h-20 bg-teal-500/20 rounded-full blur-xl\"\n        />\n        <motion.div\n          animate={{ \n            y: [0, 30, 0],\n            rotate: [0, -5, 0]\n          }}\n          transition={{ \n            duration: 8,\n            repeat: Infinity,\n            ease: \"easeInOut\"\n          }}\n          className=\"absolute bottom-20 right-10 w-32 h-32 bg-blue-500/20 rounded-full blur-xl\"\n        />\n      </div>\n      \n      <div className=\"container mx-auto px-6 relative z-10\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          {/* Main Content */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"mb-12\"\n          >\n            <div className=\"flex items-center justify-center mb-6\">\n              <Mail className=\"h-16 w-16 text-teal-400 mr-4\" />\n              <h1 className=\"text-5xl md:text-7xl font-bold text-white\">\n                Contact <span className=\"text-teal-400\">Us</span>\n              </h1>\n            </div>\n            <div className=\"h-1 w-32 bg-gradient-to-r from-teal-400 to-blue-400 mx-auto rounded-full mb-8\"></div>\n          </motion.div>\n\n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"text-xl md:text-2xl text-slate-300 mb-12 leading-relaxed max-w-3xl mx-auto\"\n          >\n            Ready to explore investment opportunities or learn more about our services? \n            Get in touch with our global team for personalized consultation and expert guidance.\n          </motion.p>\n\n          {/* Quick Contact Info */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-16\"\n          >\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300\">\n              <Mail className=\"h-12 w-12 text-teal-400 mx-auto mb-4\" />\n              <h3 className=\"text-xl font-bold text-white mb-2\">Email Us</h3>\n              <a href=\"mailto:<EMAIL>\" className=\"text-teal-300 hover:text-teal-200 transition-colors duration-300\">\n                <EMAIL>\n              </a>\n            </div>\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300\">\n              <Phone className=\"h-12 w-12 text-teal-400 mx-auto mb-4\" />\n              <h3 className=\"text-xl font-bold text-white mb-2\">Call Us</h3>\n              <div className=\"text-teal-300\">\n                <div>USA: +****************</div>\n                <div>Malaysia: +60 3 6201 5195</div>\n              </div>\n            </div>\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300\">\n              <MapPin className=\"h-12 w-12 text-teal-400 mx-auto mb-4\" />\n              <h3 className=\"text-xl font-bold text-white mb-2\">Visit Us</h3>\n              <p className=\"text-teal-300 text-sm\">\n                Global offices in USA, Malaysia, and Thailand\n              </p>\n            </div>\n          </motion.div>\n\n          {/* CTA */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center\"\n          >\n            <button className=\"bg-teal-500 hover:bg-teal-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300 shadow-lg hover:shadow-xl\">\n              Schedule Consultation\n            </button>\n            <button className=\"border-2 border-white text-white hover:bg-white hover:text-teal-900 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300\">\n              Download Brochure\n            </button>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 1, delay: 1 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <motion.div\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n          className=\"w-6 h-10 border-2 border-white rounded-full flex justify-center\"\n        >\n          <motion.div\n            animate={{ y: [0, 12, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n            className=\"w-1 h-3 bg-white rounded-full mt-2\"\n          />\n        </motion.div>\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAKO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BACP,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;4BACd,QAAQ;gCAAC;gCAAG;gCAAG;6BAAE;wBACnB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;wBACA,WAAU;;;;;;kCAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BACP,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;4BACb,QAAQ;gCAAC;gCAAG,CAAC;gCAAG;6BAAE;wBACpB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;wBACA,WAAU;;;;;;;;;;;;0BAId,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAG,WAAU;;gDAA4C;8DAChD,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;8CAG5C,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;sCAMD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAE,MAAK;4CAA+B,WAAU;sDAAmE;;;;;;;;;;;;8CAItH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAI;;;;;;8DACL,8OAAC;8DAAI;;;;;;;;;;;;;;;;;;8CAGT,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAOzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAO,WAAU;8CAA+I;;;;;;8CAGjK,8OAAC;oCAAO,WAAU;8CAA6I;;;;;;;;;;;;;;;;;;;;;;;0BAQrK,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;oBAAG,OAAO;gBAAE;gBACpC,WAAU;0BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;oBAC5C,WAAU;8BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBAAC;wBACzB,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;wBAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMtB", "debugId": null}}, {"offset": {"line": 1281, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/sections/contact/contact-form.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Send, User, Mail, MessageSquare, Building } from \"lucide-react\";\nimport { useState } from \"react\";\n\nexport function ContactForm() {\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    company: \"\",\n    subject: \"\",\n    message: \"\",\n    service: \"\"\n  });\n\n  const services = [\n    \"Investment & Research\",\n    \"Business Development\", \n    \"Financial Advisory\",\n    \"PPE Manufacturing\",\n    \"General Inquiry\"\n  ];\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // Handle form submission here\n    console.log(\"Form submitted:\", formData);\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold text-slate-900 mb-6\">\n            Get in <span className=\"text-teal-500\">Touch</span>\n          </h2>\n          <p className=\"text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed\">\n            Ready to explore opportunities with KleverCo? Send us a message and our team \n            will get back to you within 24 hours.\n          </p>\n        </motion.div>\n\n        <div className=\"max-w-4xl mx-auto\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"bg-gradient-to-br from-slate-50 to-blue-50 rounded-3xl p-12 shadow-lg border border-slate-100\"\n          >\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {/* Name */}\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-semibold text-slate-700 mb-2\">\n                    Full Name *\n                  </label>\n                  <div className=\"relative\">\n                    <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400\" />\n                    <input\n                      type=\"text\"\n                      id=\"name\"\n                      name=\"name\"\n                      value={formData.name}\n                      onChange={handleChange}\n                      required\n                      className=\"w-full pl-12 pr-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all duration-300 bg-white\"\n                      placeholder=\"Enter your full name\"\n                    />\n                  </div>\n                </div>\n\n                {/* Email */}\n                <div>\n                  <label htmlFor=\"email\" className=\"block text-sm font-semibold text-slate-700 mb-2\">\n                    Email Address *\n                  </label>\n                  <div className=\"relative\">\n                    <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400\" />\n                    <input\n                      type=\"email\"\n                      id=\"email\"\n                      name=\"email\"\n                      value={formData.email}\n                      onChange={handleChange}\n                      required\n                      className=\"w-full pl-12 pr-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all duration-300 bg-white\"\n                      placeholder=\"Enter your email address\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {/* Company */}\n                <div>\n                  <label htmlFor=\"company\" className=\"block text-sm font-semibold text-slate-700 mb-2\">\n                    Company/Organization\n                  </label>\n                  <div className=\"relative\">\n                    <Building className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400\" />\n                    <input\n                      type=\"text\"\n                      id=\"company\"\n                      name=\"company\"\n                      value={formData.company}\n                      onChange={handleChange}\n                      className=\"w-full pl-12 pr-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all duration-300 bg-white\"\n                      placeholder=\"Enter your company name\"\n                    />\n                  </div>\n                </div>\n\n                {/* Service Interest */}\n                <div>\n                  <label htmlFor=\"service\" className=\"block text-sm font-semibold text-slate-700 mb-2\">\n                    Service of Interest\n                  </label>\n                  <select\n                    id=\"service\"\n                    name=\"service\"\n                    value={formData.service}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all duration-300 bg-white\"\n                  >\n                    <option value=\"\">Select a service</option>\n                    {services.map((service) => (\n                      <option key={service} value={service}>\n                        {service}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n\n              {/* Subject */}\n              <div>\n                <label htmlFor=\"subject\" className=\"block text-sm font-semibold text-slate-700 mb-2\">\n                  Subject *\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"subject\"\n                  name=\"subject\"\n                  value={formData.subject}\n                  onChange={handleChange}\n                  required\n                  className=\"w-full px-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all duration-300 bg-white\"\n                  placeholder=\"Enter the subject of your inquiry\"\n                />\n              </div>\n\n              {/* Message */}\n              <div>\n                <label htmlFor=\"message\" className=\"block text-sm font-semibold text-slate-700 mb-2\">\n                  Message *\n                </label>\n                <div className=\"relative\">\n                  <MessageSquare className=\"absolute left-3 top-4 h-5 w-5 text-slate-400\" />\n                  <textarea\n                    id=\"message\"\n                    name=\"message\"\n                    value={formData.message}\n                    onChange={handleChange}\n                    required\n                    rows={6}\n                    className=\"w-full pl-12 pr-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all duration-300 bg-white resize-none\"\n                    placeholder=\"Tell us about your inquiry, project, or how we can help you...\"\n                  />\n                </div>\n              </div>\n\n              {/* Submit Button */}\n              <div className=\"text-center\">\n                <motion.button\n                  type=\"submit\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"bg-gradient-to-r from-teal-500 to-blue-500 hover:from-teal-600 hover:to-blue-600 text-white px-12 py-4 rounded-lg font-semibold text-lg transition-all duration-300 shadow-lg hover:shadow-xl flex items-center mx-auto\"\n                >\n                  Send Message\n                  <Send className=\"h-5 w-5 ml-2\" />\n                </motion.button>\n              </div>\n            </form>\n\n            {/* Additional Info */}\n            <div className=\"mt-12 pt-8 border-t border-slate-200\">\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 text-center\">\n                <div>\n                  <h4 className=\"font-bold text-slate-900 mb-2\">Response Time</h4>\n                  <p className=\"text-slate-600 text-sm\">We respond within 24 hours</p>\n                </div>\n                <div>\n                  <h4 className=\"font-bold text-slate-900 mb-2\">Consultation</h4>\n                  <p className=\"text-slate-600 text-sm\">Free initial consultation</p>\n                </div>\n                <div>\n                  <h4 className=\"font-bold text-slate-900 mb-2\">Global Support</h4>\n                  <p className=\"text-slate-600 text-sm\">Available across all time zones</p>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;IACX;IAEA,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,8BAA8B;QAC9B,QAAQ,GAAG,CAAC,mBAAmB;IACjC;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;;gCAAqD;8CAC1D,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAEzC,8OAAC;4BAAE,WAAU;sCAA2D;;;;;;;;;;;;8BAM1E,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAO,WAAU;kEAAkD;;;;;;kEAGlF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAMlB,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAQ,WAAU;kEAAkD;;;;;;kEAGnF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;kDAMpB,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAAkD;;;;;;kEAGrF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,OAAO;gEACvB,UAAU;gEACV,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAMlB,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAAkD;;;;;;kEAGrF,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;oEAAqB,OAAO;8EAC1B;mEADU;;;;;;;;;;;;;;;;;;;;;;;kDASrB,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAU,WAAU;0DAAkD;;;;;;0DAGrF,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,OAAO;gDACvB,UAAU;gDACV,QAAQ;gDACR,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAKhB,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAU,WAAU;0DAAkD;;;;;;0DAGrF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;kEACzB,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,QAAQ;wDACR,MAAM;wDACN,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAMlB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,MAAK;4CACL,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,WAAU;;gDACX;8DAEC,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAMtB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAgC;;;;;;8DAC9C,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;sDAExC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAgC;;;;;;8DAC9C,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;sDAExC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAgC;;;;;;8DAC9C,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxD", "debugId": null}}, {"offset": {"line": 1838, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/sections/contact/office-locations.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { MapPin, Phone, Clock, Building } from \"lucide-react\";\n\nconst offices = [\n  {\n    name: \"KLEVER Global\",\n    country: \"United States\",\n    type: \"Global Headquarters\",\n    address: \"30N Gould Street, ste R\",\n    city: \"Sheridan, WY 82801, USA\",\n    phone: \"+****************\",\n    email: \"<EMAIL>\",\n    hours: \"Mon-Fri: 9:00 AM - 6:00 PM (MST)\",\n    color: \"from-blue-500 to-indigo-500\",\n    services: [\"Investment Research\", \"Strategic Advisory\", \"Global Operations\"]\n  },\n  {\n    name: \"KLEVER SDN. BHD.\",\n    country: \"Malaysia\", \n    type: \"Regional Operations\",\n    address: \"21-9 Office Suites, 1 Mont' Kiara\",\n    city: \"Mont Kiara, 50480 Kuala Lumpur, Malaysia\",\n    phone: \"+60 3 6201 5195\",\n    email: \"<EMAIL>\",\n    hours: \"Mon-Fri: 9:00 AM - 6:00 PM (MYT)\",\n    color: \"from-green-500 to-emerald-500\",\n    services: [\"Business Development\", \"Regional Advisory\", \"Market Research\"]\n  },\n  {\n    name: \"KLEVER Thai Hua Gloves\",\n    country: \"Thailand\",\n    type: \"Manufacturing Hub\",\n    address: \"238/10 Ratchada-Pisek Road\",\n    city: \"Huai-Khwang, Bangkok 10310, Thailand\",\n    phone: \"\",\n    email: \"<EMAIL>\",\n    hours: \"Mon-Fri: 8:00 AM - 5:00 PM (ICT)\",\n    color: \"from-red-500 to-pink-500\",\n    services: [\"PPE Manufacturing\", \"Quality Control\", \"Distribution\"]\n  }\n];\n\nexport function OfficeLocations() {\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-slate-50 to-blue-50\">\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold text-slate-900 mb-6\">\n            Our Global <span className=\"text-teal-500\">Offices</span>\n          </h2>\n          <p className=\"text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed\">\n            With strategic locations across three continents, we're positioned to serve \n            clients worldwide with local expertise and global perspective.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {offices.map((office, index) => (\n            <motion.div\n              key={office.name}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"group\"\n            >\n              <div className=\"bg-white rounded-3xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 h-full\">\n                {/* Header */}\n                <div className=\"text-center mb-6\">\n                  <div className={`w-16 h-16 bg-gradient-to-r ${office.color} rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>\n                    <Building className=\"h-8 w-8 text-white\" />\n                  </div>\n                  <h3 className=\"text-2xl font-bold text-slate-900 group-hover:text-teal-600 transition-colors duration-300 mb-2\">\n                    {office.name}\n                  </h3>\n                  <p className=\"text-teal-600 font-semibold\">{office.type}</p>\n                  <p className=\"text-slate-500 text-lg font-medium\">{office.country}</p>\n                </div>\n\n                {/* Address */}\n                <div className=\"mb-6\">\n                  <div className=\"flex items-start mb-3\">\n                    <MapPin className=\"h-5 w-5 text-slate-400 mr-3 mt-1 flex-shrink-0\" />\n                    <div>\n                      <p className=\"text-slate-700 font-medium\">{office.address}</p>\n                      <p className=\"text-slate-600\">{office.city}</p>\n                    </div>\n                  </div>\n                  \n                  {office.phone && (\n                    <div className=\"flex items-center mb-3\">\n                      <Phone className=\"h-5 w-5 text-slate-400 mr-3\" />\n                      <a href={`tel:${office.phone}`} className=\"text-slate-600 hover:text-teal-600 transition-colors duration-300\">\n                        {office.phone}\n                      </a>\n                    </div>\n                  )}\n\n                  <div className=\"flex items-center\">\n                    <Clock className=\"h-5 w-5 text-slate-400 mr-3\" />\n                    <p className=\"text-slate-600 text-sm\">{office.hours}</p>\n                  </div>\n                </div>\n\n                {/* Services */}\n                <div className=\"mb-6\">\n                  <h4 className=\"text-lg font-bold text-slate-900 mb-3\">Services</h4>\n                  <div className=\"space-y-2\">\n                    {office.services.map((service, idx) => (\n                      <div key={idx} className=\"flex items-center\">\n                        <div className={`w-2 h-2 bg-gradient-to-r ${office.color} rounded-full mr-3`}></div>\n                        <span className=\"text-slate-700 text-sm\">{service}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* CTA */}\n                <div className=\"space-y-3\">\n                  <button className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 bg-gradient-to-r ${office.color} text-white hover:shadow-lg hover:scale-105`}>\n                    Contact Office\n                  </button>\n                  <a \n                    href={`mailto:${office.email}`}\n                    className=\"block w-full py-3 rounded-lg font-semibold transition-all duration-300 border-2 border-slate-200 text-slate-700 hover:border-teal-500 hover:text-teal-600 text-center\"\n                  >\n                    Send Email\n                  </a>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"mt-20\"\n        >\n          <div className=\"bg-gradient-to-r from-teal-600 to-blue-600 rounded-3xl p-12 text-white text-center\">\n            <h3 className=\"text-3xl md:text-4xl font-bold mb-6\">\n              Ready to Connect?\n            </h3>\n            <p className=\"text-xl text-teal-100 mb-8 max-w-2xl mx-auto\">\n              Whether you're interested in investment opportunities, business development, \n              or our manufacturing capabilities, we're here to help.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <a \n                href=\"mailto:<EMAIL>\"\n                className=\"bg-white text-teal-600 hover:bg-teal-50 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300 shadow-lg\"\n              >\n                <EMAIL>\n              </a>\n              <button className=\"border-2 border-white text-white hover:bg-white hover:text-teal-600 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300\">\n                Schedule Meeting\n              </button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,UAAU;IACd;QACE,MAAM;QACN,SAAS;QACT,MAAM;QACN,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,UAAU;YAAC;YAAuB;YAAsB;SAAoB;IAC9E;IACA;QACE,MAAM;QACN,SAAS;QACT,MAAM;QACN,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,UAAU;YAAC;YAAwB;YAAqB;SAAkB;IAC5E;IACA;QACE,MAAM;QACN,SAAS;QACT,MAAM;QACN,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,UAAU;YAAC;YAAqB;YAAmB;SAAe;IACpE;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;;gCAAqD;8CACtD,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAE7C,8OAAC;4BAAE,WAAU;sCAA2D;;;;;;;;;;;;8BAM1E,8OAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,2BAA2B,EAAE,OAAO,KAAK,CAAC,mHAAmH,CAAC;0DAC7K,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,8OAAC;gDAAG,WAAU;0DACX,OAAO,IAAI;;;;;;0DAEd,8OAAC;gDAAE,WAAU;0DAA+B,OAAO,IAAI;;;;;;0DACvD,8OAAC;gDAAE,WAAU;0DAAsC,OAAO,OAAO;;;;;;;;;;;;kDAInE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAA8B,OAAO,OAAO;;;;;;0EACzD,8OAAC;gEAAE,WAAU;0EAAkB,OAAO,IAAI;;;;;;;;;;;;;;;;;;4CAI7C,OAAO,KAAK,kBACX,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAE,MAAM,CAAC,IAAI,EAAE,OAAO,KAAK,EAAE;wDAAE,WAAU;kEACvC,OAAO,KAAK;;;;;;;;;;;;0DAKnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAE,WAAU;kEAA0B,OAAO,KAAK;;;;;;;;;;;;;;;;;;kDAKvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,8OAAC;gDAAI,WAAU;0DACZ,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC7B,8OAAC;wDAAc,WAAU;;0EACvB,8OAAC;gEAAI,WAAW,CAAC,yBAAyB,EAAE,OAAO,KAAK,CAAC,kBAAkB,CAAC;;;;;;0EAC5E,8OAAC;gEAAK,WAAU;0EAA0B;;;;;;;uDAFlC;;;;;;;;;;;;;;;;kDAShB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAO,WAAW,CAAC,kFAAkF,EAAE,OAAO,KAAK,CAAC,2CAA2C,CAAC;0DAAE;;;;;;0DAGnK,8OAAC;gDACC,MAAM,CAAC,OAAO,EAAE,OAAO,KAAK,EAAE;gDAC9B,WAAU;0DACX;;;;;;;;;;;;;;;;;;2BAlEA,OAAO,IAAI;;;;;;;;;;8BA4EtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAI5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCAAO,WAAU;kDAA6I;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7K", "debugId": null}}]}