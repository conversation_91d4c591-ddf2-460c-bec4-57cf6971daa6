"use client";

import { motion } from "framer-motion";
import { TrendingUp, Building, Users, Heart, Globe } from "lucide-react";

const businessDivisions = [
  {
    title: "We Develop Business",
    subtitle: "Facilitate Business Growth",
    description: "Global investment and research boutique firm providing strategic solutions for business development and growth facilitation.",
    icon: TrendingUp,
    color: "from-blue-500 to-indigo-500"
  },
  {
    title: "We Support Business",
    subtitle: "Business Innovation and Support",
    description: "Comprehensive business innovation and support services helping companies achieve sustainable growth and operational excellence.",
    icon: Building,
    color: "from-green-500 to-emerald-500"
  },
  {
    title: "We Advise",
    subtitle: "Expert Financial Advice",
    description: "Expert financial advice and strategic guidance for complex financial decisions and investment opportunities across global markets.",
    icon: Users,
    color: "from-purple-500 to-violet-500"
  },
  {
    title: "We Serve Humanity",
    subtitle: "HEALTH",
    description: "State-of-the-art PPE nitrile glove manufacturing serving global healthcare needs with highest quality standards and safety solutions.",
    icon: Heart,
    color: "from-red-500 to-pink-500"
  }
];

export function BusinessDivisions() {
  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
            Our <span className="text-teal-500">Business Divisions</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            KleverCo operates across multiple business verticals, delivering comprehensive 
            solutions that drive growth, innovation, and value creation globally.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {businessDivisions.map((division, index) => {
            const Icon = division.icon;
            
            return (
              <motion.div
                key={division.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="bg-white rounded-3xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 h-full">
                  {/* Header */}
                  <div className="flex items-start mb-6">
                    <div className={`w-16 h-16 bg-gradient-to-r ${division.color} rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-slate-900 group-hover:text-teal-600 transition-colors duration-300 mb-1">
                        {division.title}
                      </h3>
                      <p className="text-teal-600 font-semibold text-lg">{division.subtitle}</p>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-slate-600 leading-relaxed mb-6">
                    {division.description}
                  </p>

                  {/* CTA */}
                  <button className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 bg-gradient-to-r ${division.color} text-white hover:shadow-lg hover:scale-105`}>
                    Learn More
                  </button>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Bottom Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="bg-gradient-to-r from-teal-600 to-blue-600 rounded-3xl p-12 text-white text-center"
        >
          <Globe className="h-16 w-16 text-teal-200 mx-auto mb-6" />
          <h3 className="text-3xl md:text-4xl font-bold mb-6">
            Global Presence
          </h3>
          <p className="text-xl text-teal-100 mb-8 max-w-2xl mx-auto">
            Operating across multiple continents with strategic locations for investment, research, and manufacturing excellence.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
              <div className="text-2xl font-bold mb-2">KLEVER Global</div>
              <div className="text-teal-200">30N Gould Street, ste R</div>
              <div className="text-teal-200">Sheridan, WY 82801, USA</div>
              <div className="text-sm text-teal-300 mt-2">+****************</div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
              <div className="text-2xl font-bold mb-2">KLEVER SDN. BHD.</div>
              <div className="text-teal-200">21-9 Office Suites, 1 Mont' Kiara</div>
              <div className="text-teal-200">Mont Kiara, 50480 Kuala Lumpur, Malaysia</div>
              <div className="text-sm text-teal-300 mt-2">+60 3 6201 5195</div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
              <div className="text-2xl font-bold mb-2">KLEVER Thai Hua Gloves</div>
              <div className="text-teal-200">238/10 Ratchada-Pisek Road</div>
              <div className="text-teal-200">Huai-Khwang, Bangkok 10310, Thailand</div>
              <div className="text-sm text-teal-300 mt-2">Manufacturing Hub</div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
