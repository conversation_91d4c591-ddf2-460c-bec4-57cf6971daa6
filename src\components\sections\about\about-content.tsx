"use client";

import { motion } from "framer-motion";
import { Lightbulb, MessageCircle, HandHeart, ArrowRightLeft, DollarSign } from "lucide-react";

export function AboutContent() {
  return (
    <section className="py-32 bg-white">
      <div className="container mx-auto px-6">
        <div className="max-w-6xl mx-auto">
          {/* Main Description */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-20"
          >
            <div className="bg-slate-50 border border-slate-100 rounded-2xl p-8 md:p-12">
              <p className="text-lg text-slate-700 leading-relaxed font-light mb-6">
                We operate in Investment Management, Private Equity and Project Funding. Our Venture Capital supports 
                startups, small businesses to large scale projects that have short to medium to long term exponential 
                growth potential.
              </p>
              <p className="text-lg text-slate-700 leading-relaxed font-light mb-6">
                We identify, invest and develop opportunities worldwide spread across diversified fields. We have a 
                global footprint across industries. Our team specializes in qualitative and quantitative growth. Our 
                projects are span over variant term tiers. We drive upon investing in data-driven methodology by 
                combining cutting-edge analytical technology with strong data analytics.
              </p>
            </div>
          </motion.div>

          {/* Core Values */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
            {/* Innovative */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-slate-50 border border-slate-100 rounded-2xl p-8 hover:shadow-lg hover:border-slate-200 transition-all duration-300 h-full">
                <div className="w-14 h-14 bg-slate-900 rounded-xl flex items-center justify-center mb-6 group-hover:bg-slate-800 transition-colors duration-300">
                  <Lightbulb className="h-7 w-7 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-slate-900 mb-4 group-hover:text-slate-800 transition-colors duration-300">
                  Innovative
                </h3>
                <p className="text-slate-600 font-light leading-relaxed">
                  We promote Innovative asset management services by providing breeding grounds to create and nurture 
                  ideas. We support logical analysis that gives birth to new perspectives. It translates into 
                  innovating products and creates new opportunities across avenues to venture upon.
                </p>
              </div>
            </motion.div>

            {/* Advise */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-slate-50 border border-slate-100 rounded-2xl p-8 hover:shadow-lg hover:border-slate-200 transition-all duration-300 h-full">
                <div className="w-14 h-14 bg-slate-900 rounded-xl flex items-center justify-center mb-6 group-hover:bg-slate-800 transition-colors duration-300">
                  <MessageCircle className="h-7 w-7 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-slate-900 mb-4 group-hover:text-slate-800 transition-colors duration-300">
                  Advise
                </h3>
                <p className="text-slate-600 font-light leading-relaxed">
                  We advise to invest in businesses with real time growth potential. We facilitate and get involved 
                  in inter-governmental projects and take them all the way to finish line.
                </p>
              </div>
            </motion.div>

            {/* Support */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-slate-50 border border-slate-100 rounded-2xl p-8 hover:shadow-lg hover:border-slate-200 transition-all duration-300 h-full">
                <div className="w-14 h-14 bg-slate-900 rounded-xl flex items-center justify-center mb-6 group-hover:bg-slate-800 transition-colors duration-300">
                  <HandHeart className="h-7 w-7 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-slate-900 mb-4 group-hover:text-slate-800 transition-colors duration-300">
                  Support
                </h3>
                <p className="text-slate-600 font-light leading-relaxed">
                  We facilitate market's efficiency and manoeuvre liquidity. We equip investors and companies to 
                  achieve their required goals, whether it being to invest, money raise, off load or to risk mitigation.
                </p>
              </div>
            </motion.div>

            {/* Transact */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-slate-50 border border-slate-100 rounded-2xl p-8 hover:shadow-lg hover:border-slate-200 transition-all duration-300 h-full">
                <div className="w-14 h-14 bg-slate-900 rounded-xl flex items-center justify-center mb-6 group-hover:bg-slate-800 transition-colors duration-300">
                  <ArrowRightLeft className="h-7 w-7 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-slate-900 mb-4 group-hover:text-slate-800 transition-colors duration-300">
                  Transact
                </h3>
                <p className="text-slate-600 font-light leading-relaxed">
                  We transact for ourselves and our partnering entities in all key market sectors including but not 
                  limited to equities, commodities and projects. We support from startups to buyouts to spinoffs.
                </p>
              </div>
            </motion.div>

            {/* Finance */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-slate-50 border border-slate-100 rounded-2xl p-8 hover:shadow-lg hover:border-slate-200 transition-all duration-300 h-full">
                <div className="w-14 h-14 bg-slate-900 rounded-xl flex items-center justify-center mb-6 group-hover:bg-slate-800 transition-colors duration-300">
                  <DollarSign className="h-7 w-7 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-slate-900 mb-4 group-hover:text-slate-800 transition-colors duration-300">
                  Finance
                </h3>
                <p className="text-slate-600 font-light leading-relaxed">
                  We encourage and support by providing a platform to local, regional and national governments to 
                  finance their operations in order to run their systems smoothly. We also facilitate investments 
                  in private sector.
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
