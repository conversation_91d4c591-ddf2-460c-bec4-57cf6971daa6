"use client";

import { motion } from "framer-motion";
import { useState } from "react";
import { Lightbulb, MessageCircle, HandHeart, ArrowRightLeft, DollarSign, ChevronDown, ChevronUp } from "lucide-react";

const coreValues = [
  {
    id: "innovative",
    title: "Innovative",
    icon: Lightbulb,
    description: "We promote Innovative asset management services by providing breeding grounds to create and nurture ideas. We support logical analysis that gives birth to new perspectives. It translates into innovating products and creates new opportunities across avenues to venture upon."
  },
  {
    id: "advise",
    title: "Advise",
    icon: MessageCircle,
    description: "We advise to invest in businesses with real time growth potential. We facilitate and get involved in inter-governmental projects and take them all the way to finish line."
  },
  {
    id: "support",
    title: "Support",
    icon: HandHeart,
    description: "We facilitate market's efficiency and manoeuvre liquidity. We equip investors and companies to achieve their required goals, whether it being to invest, money raise, off load or to risk mitigation."
  },
  {
    id: "transact",
    title: "Transact",
    icon: ArrowRightLeft,
    description: "We transact for ourselves and our partnering entities in all key market sectors including but not limited to equities, commodities and projects. We support from startups to buyouts to spinoffs."
  },
  {
    id: "finance",
    title: "Finance",
    icon: DollarSign,
    description: "We encourage and support by providing a platform to local, regional and national governments to finance their operations in order to run their systems smoothly. We also facilitate investments in private sector."
  }
];

export function AboutContent() {
  const [expandedValue, setExpandedValue] = useState<string | null>(null);

  const toggleExpanded = (valueId: string) => {
    setExpandedValue(expandedValue === valueId ? null : valueId);
  };

  return (
    <section className="py-32 bg-white">
      <div className="container mx-auto px-6">
        <div className="max-w-6xl mx-auto">
          {/* Main Description */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-20"
          >
            <div className="bg-slate-50 border border-slate-100 rounded-2xl p-8 md:p-12">
              <p className="text-lg text-slate-700 leading-relaxed font-light mb-6">
                We operate in Investment Management, Private Equity and Project Funding. Our Venture Capital supports
                startups, small businesses to large scale projects that have short to medium to long term exponential
                growth potential.
              </p>
              <p className="text-lg text-slate-700 leading-relaxed font-light mb-6">
                We identify, invest and develop opportunities worldwide spread across diversified fields. We have a
                global footprint across industries. Our team specializes in qualitative and quantitative growth. Our
                projects are span over variant term tiers. We drive upon investing in data-driven methodology by
                combining cutting-edge analytical technology with strong data analytics.
              </p>
            </div>
          </motion.div>

          {/* Core Values Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-light text-slate-900 mb-6 tracking-tight">
              Our <span className="font-normal text-slate-600">Core Values</span>
            </h2>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto mb-8"></div>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto leading-relaxed font-light">
              Five fundamental principles that guide our approach to investment, innovation, and client service.
            </p>
          </motion.div>

          {/* Interactive Core Values */}
          <div className="space-y-4">
            {coreValues.map((value, index) => {
              const Icon = value.icon;
              const isExpanded = expandedValue === value.id;

              return (
                <motion.div
                  key={value.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="group"
                >
                  <div className="bg-slate-50 border border-slate-100 rounded-2xl overflow-hidden hover:shadow-lg hover:border-slate-200 transition-all duration-300">
                    {/* Header - Always Visible */}
                    <button
                      onClick={() => toggleExpanded(value.id)}
                      className="w-full p-6 md:p-8 flex items-center justify-between text-left hover:bg-slate-100 transition-colors duration-300"
                    >
                      <div className="flex items-center space-x-6">
                        <div className="w-12 h-12 bg-slate-900 rounded-xl flex items-center justify-center group-hover:bg-slate-800 transition-colors duration-300 flex-shrink-0">
                          <Icon className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="text-xl md:text-2xl font-semibold text-slate-900 group-hover:text-slate-800 transition-colors duration-300">
                          {value.title}
                        </h3>
                      </div>
                      <div className="flex-shrink-0 ml-4">
                        {isExpanded ? (
                          <ChevronUp className="h-6 w-6 text-slate-600 transition-transform duration-300" />
                        ) : (
                          <ChevronDown className="h-6 w-6 text-slate-600 transition-transform duration-300" />
                        )}
                      </div>
                    </button>

                    {/* Expandable Content */}
                    <motion.div
                      initial={false}
                      animate={{
                        height: isExpanded ? "auto" : 0,
                        opacity: isExpanded ? 1 : 0
                      }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                      className="overflow-hidden"
                    >
                      <div className="px-6 md:px-8 pb-6 md:pb-8">
                        <div className="pl-18">
                          <p className="text-slate-600 font-light leading-relaxed text-lg">
                            {value.description}
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
