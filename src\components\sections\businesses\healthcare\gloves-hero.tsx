"use client";

import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { ArrowLeft, Globe } from "lucide-react";

export function GlovesHero() {
  return (
    <section className="relative pt-32 pb-12 bg-gradient-to-b from-slate-900 to-slate-800 overflow-hidden">
      {/* Subtle background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,.1)_50%,transparent_75%,transparent_100%)] bg-[length:20px_20px]"></div>
      </div>

      {/* Manufacturing-themed floating elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 0.08, scale: 1 }}
          transition={{ duration: 2, ease: "easeOut" }}
          className="absolute top-20 right-20 w-32 h-32 border border-orange-400/30 rounded-full"
        />
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 0.06, y: 0 }}
          transition={{ duration: 2.5, ease: "easeOut" }}
          className="absolute bottom-24 left-24 w-24 h-24 bg-orange-600/20 rounded-lg"
        />
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Breadcrumb Navigation */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-12"
          >
            <Link
              href="/businesses/healthcare"
              className="inline-flex items-center text-slate-400 hover:text-white transition-colors group"
            >
              <ArrowLeft className="h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform" />
              <span className="text-sm font-light">Back to Healthcare</span>
            </Link>
          </motion.div>

          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-center mb-20"
          >
            <div className="flex justify-center mb-8">
              <div className="bg-orange-500 p-4 rounded-2xl">
                <Globe className="h-12 w-12 text-white" />
              </div>
            </div>
            
            <h1 className="text-5xl md:text-7xl font-light text-white mb-4 tracking-tight">
              GLOVES
            </h1>
            <p className="text-2xl text-slate-400 mb-8 font-light">
              Klever Gloves
            </p>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-400 to-transparent mx-auto mb-8"></div>
            <p className="text-xl text-slate-500 max-w-3xl mx-auto leading-relaxed font-light">
              State-of-the-art PPE nitrile glove manufacturing with vertically integrated business model and global distribution capabilities
            </p>
          </motion.div>

          {/* Key Features */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {[
              { value: "3 Countries", label: "USA, Malaysia, Thailand" },
              { value: "Vertically Integrated", label: "Complete supply chain" },
              { value: "PPE Manufacturing", label: "Nitrile gloves production" }
            ].map((feature, index) => (
              <div key={feature.label} className="text-center">
                <div className="text-2xl md:text-3xl font-light text-white mb-2 tracking-tight">
                  {feature.value}
                </div>
                <p className="text-slate-400 font-light">
                  {feature.label}
                </p>
              </div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
}
