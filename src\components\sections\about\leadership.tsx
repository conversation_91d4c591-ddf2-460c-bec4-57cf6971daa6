"use client";

import { motion } from "framer-motion";
import { Linkedin, Mail, Award, GraduationCap } from "lucide-react";

const leaders = [
  {
    name: "<PERSON>",
    position: "Chief Executive Officer & Managing Partner",
    image: "/api/placeholder/300/400",
    bio: "<PERSON> brings over 20 years of investment banking and private equity experience, having led over $15B in transactions across technology and healthcare sectors.",
    education: "MBA Harvard Business School, BS Stanford University",
    achievements: ["Forbes 40 Under 40", "PE Professional of the Year 2023"],
    linkedin: "#",
    email: "<EMAIL>"
  },
  {
    name: "<PERSON>",
    position: "Chief Investment Officer",
    image: "/api/placeholder/300/400",
    bio: "<PERSON> oversees KleverCo's investment strategy with expertise in emerging markets and sustainable investing, managing our $12.5B portfolio.",
    education: "PhD Economics MIT, MBA Wharton",
    achievements: ["CFA Institute Recognition", "Sustainable Investor Award"],
    linkedin: "#",
    email: "<EMAIL>"
  },
  {
    name: "<PERSON>. <PERSON>",
    position: "Head of Healthcare Investments",
    image: "/api/placeholder/300/400",
    bio: "<PERSON> leads our healthcare portfolio with deep expertise in biotechnology and medical devices, having founded two successful biotech companies.",
    education: "<PERSON>, PhD Bioengineering",
    achievements: ["Healthcare Innovation Award", "Biotech Entrepreneur of the Year"],
    linkedin: "#",
    email: "<EMAIL>"
  },
  {
    name: "James Thompson",
    position: "Head of Technology Investments",
    image: "/api/placeholder/300/400",
    bio: "James drives our technology investment strategy, specializing in AI, cybersecurity, and fintech with over 15 years in Silicon Valley.",
    education: "MS Computer Science Stanford, BS MIT",
    achievements: ["Tech Investor of the Year", "AI Innovation Recognition"],
    linkedin: "#",
    email: "<EMAIL>"
  }
];

export function Leadership() {
  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
            Leadership <span className="text-teal-500">Team</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            Meet the visionary leaders driving KleverCo's success through decades of combined 
            experience, strategic insight, and unwavering commitment to excellence.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          {leaders.map((leader, index) => (
            <motion.div
              key={leader.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-white rounded-3xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
                {/* Profile Image */}
                <div className="relative mb-6">
                  <div className="w-32 h-32 mx-auto rounded-full bg-gradient-to-r from-teal-400 to-blue-400 p-1">
                    <div className="w-full h-full rounded-full bg-slate-200 flex items-center justify-center">
                      <span className="text-2xl font-bold text-slate-600">
                        {leader.name.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                  </div>
                  <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 bg-teal-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Leader
                  </div>
                </div>

                {/* Basic Info */}
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold text-slate-900 mb-2">{leader.name}</h3>
                  <p className="text-teal-600 font-semibold mb-4">{leader.position}</p>
                  <p className="text-slate-600 leading-relaxed">{leader.bio}</p>
                </div>

                {/* Education */}
                <div className="mb-6">
                  <div className="flex items-center mb-2">
                    <GraduationCap className="h-5 w-5 text-teal-500 mr-2" />
                    <span className="font-semibold text-slate-900">Education</span>
                  </div>
                  <p className="text-slate-600 text-sm">{leader.education}</p>
                </div>

                {/* Achievements */}
                <div className="mb-6">
                  <div className="flex items-center mb-2">
                    <Award className="h-5 w-5 text-teal-500 mr-2" />
                    <span className="font-semibold text-slate-900">Recognition</span>
                  </div>
                  <div className="space-y-1">
                    {leader.achievements.map((achievement, idx) => (
                      <div key={idx} className="flex items-center">
                        <div className="w-2 h-2 bg-teal-500 rounded-full mr-2"></div>
                        <span className="text-slate-600 text-sm">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Contact */}
                <div className="flex justify-center space-x-4 pt-4 border-t border-slate-100">
                  <a
                    href={leader.linkedin}
                    className="w-10 h-10 bg-blue-600 hover:bg-blue-700 rounded-full flex items-center justify-center transition-colors duration-300"
                  >
                    <Linkedin className="h-5 w-5 text-white" />
                  </a>
                  <a
                    href={`mailto:${leader.email}`}
                    className="w-10 h-10 bg-teal-600 hover:bg-teal-700 rounded-full flex items-center justify-center transition-colors duration-300"
                  >
                    <Mail className="h-5 w-5 text-white" />
                  </a>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Team Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-20"
        >
          <div className="bg-gradient-to-r from-slate-900 to-blue-900 rounded-3xl p-12 text-white">
            <div className="text-center mb-12">
              <h3 className="text-3xl font-bold mb-4">Our Team Excellence</h3>
              <p className="text-slate-300 text-lg">
                Collective expertise that drives exceptional results
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-4xl font-bold text-teal-400 mb-2">150+</div>
                <div className="text-slate-300">Team Members</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-teal-400 mb-2">500+</div>
                <div className="text-slate-300">Years Combined Experience</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-teal-400 mb-2">45+</div>
                <div className="text-slate-300">Countries Represented</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-teal-400 mb-2">25+</div>
                <div className="text-slate-300">Industry Awards</div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
