{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/navigation/navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { Button } from \"@/components/ui/button\";\nimport { TrendingUp, Menu, X, ChevronDown } from \"lucide-react\";\n\nconst navItems = [\n  { name: \"Home\", href: \"/\" },\n  {\n    name: \"About\",\n    href: \"/about\",\n    dropdown: [\n      { name: \"About Us\", href: \"/about\" },\n      { name: \"Members\", href: \"/about/members\" },\n      { name: \"Sustainability\", href: \"/about/sustainability\" },\n      { name: \"Character\", href: \"/about/character\" }\n    ]\n  },\n  {\n    name: \"Businesses\",\n    href: \"/businesses\",\n    dropdown: [\n      { name: \"All Businesses\", href: \"/businesses\" },\n      { name: \"Healthcare\", href: \"/businesses/healthcare\" },\n      { name: \"IT\", href: \"/businesses/it\" },\n      { name: \"Energy\", href: \"/businesses/energy\" },\n      { name: \"Real Estate\", href: \"/businesses/real-estate\" },\n      { name: \"Helicopters\", href: \"/businesses/helicopters\" },\n      { name: \"Food\", href: \"/businesses/food\" }\n    ]\n  },\n  { name: \"Contact\", href: \"/contact\" },\n];\n\nexport function Navbar() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  return (\n    <motion.nav\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.8 }}\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        isScrolled \n          ? \"bg-white/95 backdrop-blur-md shadow-lg border-b border-slate-200/50\" \n          : \"bg-transparent\"\n      }`}\n    >\n      <div className=\"container mx-auto px-6\">\n        <div className=\"flex items-center justify-between h-16 md:h-20\">\n          {/* Logo */}\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            className=\"flex items-center space-x-2 cursor-pointer\"\n          >\n            <TrendingUp className={`h-8 w-8 ${isScrolled ? \"text-teal-600\" : \"text-teal-400\"}`} />\n            <span className={`text-2xl font-bold ${isScrolled ? \"text-slate-900\" : \"text-white\"}`}>\n              Klever<span className={isScrolled ? \"text-teal-600\" : \"text-teal-400\"}>Co</span>\n            </span>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item, index) => (\n              <motion.div\n                key={item.name}\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"relative\"\n                onMouseEnter={() => item.dropdown && setActiveDropdown(item.name)}\n                onMouseLeave={() => setActiveDropdown(null)}\n              >\n                {item.dropdown ? (\n                  <div className=\"relative\">\n                    <button\n                      className={`font-medium transition-colors duration-300 hover:text-teal-500 flex items-center ${\n                        isScrolled ? \"text-slate-700\" : \"text-white\"\n                      }`}\n                    >\n                      {item.name}\n                      <ChevronDown className=\"h-4 w-4 ml-1\" />\n                    </button>\n\n                    <AnimatePresence>\n                      {activeDropdown === item.name && (\n                        <motion.div\n                          initial={{ opacity: 0, y: 10 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          exit={{ opacity: 0, y: 10 }}\n                          transition={{ duration: 0.2 }}\n                          className=\"absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-slate-200 py-2 z-50\"\n                        >\n                          {item.dropdown.map((dropdownItem) => (\n                            <Link\n                              key={dropdownItem.name}\n                              href={dropdownItem.href}\n                              className=\"block px-4 py-2 text-slate-700 hover:bg-teal-50 hover:text-teal-600 transition-colors duration-200\"\n                            >\n                              {dropdownItem.name}\n                            </Link>\n                          ))}\n                        </motion.div>\n                      )}\n                    </AnimatePresence>\n                  </div>\n                ) : (\n                  <Link\n                    href={item.href}\n                    className={`font-medium transition-colors duration-300 hover:text-teal-500 ${\n                      isScrolled ? \"text-slate-700\" : \"text-white\"\n                    }`}\n                  >\n                    {item.name}\n                  </Link>\n                )}\n              </motion.div>\n            ))}\n          </div>\n\n          {/* Desktop CTA */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button\n              variant={isScrolled ? \"outline\" : \"secondary\"}\n              className={`transition-all duration-300 ${\n                isScrolled \n                  ? \"border-teal-600 text-teal-600 hover:bg-teal-600 hover:text-white\" \n                  : \"bg-white/20 text-white border-white/30 hover:bg-white hover:text-slate-900\"\n              }`}\n            >\n              Get Started\n            </Button>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className={`md:hidden p-2 rounded-lg transition-colors duration-300 ${\n              isScrolled ? \"text-slate-700 hover:bg-slate-100\" : \"text-white hover:bg-white/20\"\n            }`}\n          >\n            {isMobileMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      <AnimatePresence>\n        {isMobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"md:hidden bg-white/95 backdrop-blur-md border-t border-slate-200/50\"\n          >\n            <div className=\"container mx-auto px-6 py-4\">\n              <div className=\"flex flex-col space-y-4\">\n                {navItems.map((item, index) => (\n                  <motion.div\n                    key={item.name}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ duration: 0.3, delay: index * 0.1 }}\n                  >\n                    <Link\n                      href={item.href}\n                      onClick={() => setIsMobileMenuOpen(false)}\n                      className=\"text-slate-700 font-medium py-2 hover:text-teal-600 transition-colors duration-300 block\"\n                    >\n                      {item.name}\n                    </Link>\n                  </motion.div>\n                ))}\n                <motion.div\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.3, delay: navItems.length * 0.1 }}\n                  className=\"pt-4 border-t border-slate-200\"\n                >\n                  <Button className=\"w-full bg-teal-600 hover:bg-teal-700 text-white\">\n                    Get Started\n                  </Button>\n                </motion.div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQA,MAAM,WAAW;IACf;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBAAE,MAAM;gBAAY,MAAM;YAAS;YACnC;gBAAE,MAAM;gBAAW,MAAM;YAAiB;YAC1C;gBAAE,MAAM;gBAAkB,MAAM;YAAwB;YACxD;gBAAE,MAAM;gBAAa,MAAM;YAAmB;SAC/C;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBAAE,MAAM;gBAAkB,MAAM;YAAc;YAC9C;gBAAE,MAAM;gBAAc,MAAM;YAAyB;YACrD;gBAAE,MAAM;gBAAM,MAAM;YAAiB;YACrC;gBAAE,MAAM;gBAAU,MAAM;YAAqB;YAC7C;gBAAE,MAAM;gBAAe,MAAM;YAA0B;YACvD;gBAAE,MAAM;gBAAe,MAAM;YAA0B;YACvD;gBAAE,MAAM;gBAAQ,MAAM;YAAmB;SAC1C;IACH;IACA;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAC,4DAA4D,EACtE,aACI,wEACA,kBACJ;;0BAEF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,WAAU;;8CAEV,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAW,CAAC,QAAQ,EAAE,aAAa,kBAAkB,iBAAiB;;;;;;8CAClF,8OAAC;oCAAK,WAAW,CAAC,mBAAmB,EAAE,aAAa,mBAAmB,cAAc;;wCAAE;sDAC/E,8OAAC;4CAAK,WAAW,aAAa,kBAAkB;sDAAiB;;;;;;;;;;;;;;;;;;sCAK3E,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,WAAU;oCACV,cAAc,IAAM,KAAK,QAAQ,IAAI,kBAAkB,KAAK,IAAI;oCAChE,cAAc,IAAM,kBAAkB;8CAErC,KAAK,QAAQ,iBACZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAW,CAAC,iFAAiF,EAC3F,aAAa,mBAAmB,cAChC;;oDAED,KAAK,IAAI;kEACV,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;0DAGzB,8OAAC,yLAAA,CAAA,kBAAe;0DACb,mBAAmB,KAAK,IAAI,kBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,MAAM;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC1B,YAAY;wDAAE,UAAU;oDAAI;oDAC5B,WAAU;8DAET,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,6BAClB,8OAAC,4JAAA,CAAA,UAAI;4DAEH,MAAM,aAAa,IAAI;4DACvB,WAAU;sEAET,aAAa,IAAI;2DAJb,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;6DAYlC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,+DAA+D,EACzE,aAAa,mBAAmB,cAChC;kDAED,KAAK,IAAI;;;;;;mCAhDT,KAAK,IAAI;;;;;;;;;;sCAwDpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,aAAa,YAAY;gCAClC,WAAW,CAAC,4BAA4B,EACtC,aACI,qEACA,8EACJ;0CACH;;;;;;;;;;;sCAMH,8OAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAW,CAAC,wDAAwD,EAClE,aAAa,sCAAsC,gCACnD;sCAED,iCAAmB,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMtE,8OAAC,yLAAA,CAAA,kBAAe;0BACb,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;kDAEhD,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,oBAAoB;4CACnC,WAAU;sDAET,KAAK,IAAI;;;;;;uCAVP,KAAK,IAAI;;;;;8CAclB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,SAAS,MAAM,GAAG;oCAAI;oCAC1D,WAAU;8CAEV,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;kDAAkD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtF", "debugId": null}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/sections/about/members-hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Users, Globe, Award } from \"lucide-react\";\n\nexport function MembersHero() {\n  return (\n    <section className=\"relative pt-32 pb-20 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.05%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20\"></div>\n      \n      <div className=\"container mx-auto px-6 relative z-10\">\n        <div className=\"text-center max-w-4xl mx-auto\">\n          {/* Main Heading */}\n          <motion.h1\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"text-4xl md:text-6xl font-bold text-white mb-6\"\n          >\n            Our <span className=\"text-teal-400\">Members</span>\n          </motion.h1>\n\n          {/* Subtitle */}\n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"text-xl md:text-2xl text-slate-300 mb-12 max-w-3xl mx-auto leading-relaxed\"\n          >\n            Meet the exceptional team driving KleverCo's success across global markets, \n            investment excellence, and innovative manufacturing solutions.\n          </motion.p>\n\n          {/* Stats */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto\"\n          >\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-teal-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Users className=\"h-8 w-8 text-white\" />\n              </div>\n              <h3 className=\"text-3xl font-bold text-white mb-2\">50+</h3>\n              <p className=\"text-slate-300\">Team Members</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Globe className=\"h-8 w-8 text-white\" />\n              </div>\n              <h3 className=\"text-3xl font-bold text-white mb-2\">3</h3>\n              <p className=\"text-slate-300\">Global Offices</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-purple-500 to-violet-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Award className=\"h-8 w-8 text-white\" />\n              </div>\n              <h3 className=\"text-3xl font-bold text-white mb-2\">15+</h3>\n              <p className=\"text-slate-300\">Years Experience</p>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAKO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;gCACX;8CACK,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAItC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;sCAMD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAE,WAAU;sDAAiB;;;;;;;;;;;;8CAGhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAE,WAAU;sDAAiB;;;;;;;;;;;;8CAGhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAE,WAAU;sDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C", "debugId": null}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/sections/about/team-members.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { MapPin, Briefcase } from \"lucide-react\";\n\nconst departments = [\n  {\n    name: \"Investment & Research\",\n    location: \"Global Headquarters, USA\",\n    members: [\n      { name: \"<PERSON>\", role: \"Senior Investment Analyst\", experience: \"8 years\" },\n      { name: \"<PERSON>\", role: \"Research Director\", experience: \"12 years\" },\n      { name: \"<PERSON>\", role: \"Portfolio Manager\", experience: \"10 years\" },\n      { name: \"<PERSON>\", role: \"Investment Associate\", experience: \"5 years\" }\n    ],\n    color: \"from-blue-500 to-indigo-500\"\n  },\n  {\n    name: \"Business Development\",\n    location: \"Regional Operations, Malaysia\",\n    members: [\n      { name: \"<PERSON>\", role: \"Business Development Director\", experience: \"15 years\" },\n      { name: \"<PERSON><PERSON>\", role: \"Strategic Partnerships Manager\", experience: \"9 years\" },\n      { name: \"<PERSON>\", role: \"Market Expansion Lead\", experience: \"7 years\" },\n      { name: \"<PERSON><PERSON>\", role: \"Client Relations Manager\", experience: \"6 years\" }\n    ],\n    color: \"from-green-500 to-emerald-500\"\n  },\n  {\n    name: \"Manufacturing & Operations\",\n    location: \"Manufacturing Hub, Thailand\",\n    members: [\n      { name: \"<PERSON><PERSON><PERSON>\", role: \"Manufacturing Director\", experience: \"18 years\" },\n      { name: \"<PERSON><PERSON>\", role: \"Quality Control Manager\", experience: \"11 years\" },\n      { name: \"Apinya Thanakit\", role: \"Production Supervisor\", experience: \"8 years\" },\n      { name: \"Somkid Rattana\", role: \"Supply Chain Coordinator\", experience: \"6 years\" }\n    ],\n    color: \"from-red-500 to-pink-500\"\n  },\n  {\n    name: \"Advisory & Consulting\",\n    location: \"Multi-Location Team\",\n    members: [\n      { name: \"Dr. Robert Kim\", role: \"Senior Financial Advisor\", experience: \"20 years\" },\n      { name: \"Sarah Mitchell\", role: \"Strategic Consultant\", experience: \"14 years\" },\n      { name: \"Hassan Al-Rashid\", role: \"International Markets Advisor\", experience: \"16 years\" },\n      { name: \"Maria Gonzalez\", role: \"Risk Management Specialist\", experience: \"9 years\" }\n    ],\n    color: \"from-purple-500 to-violet-500\"\n  }\n];\n\nexport function TeamMembers() {\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-slate-50 to-blue-50\">\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold text-slate-900 mb-6\">\n            Our <span className=\"text-teal-500\">Team</span>\n          </h2>\n          <p className=\"text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed\">\n            Dedicated professionals across four key departments, working together to deliver \n            exceptional results for our clients and partners worldwide.\n          </p>\n        </motion.div>\n\n        <div className=\"space-y-12\">\n          {departments.map((department, deptIndex) => (\n            <motion.div\n              key={department.name}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: deptIndex * 0.1 }}\n              viewport={{ once: true }}\n              className=\"bg-white rounded-3xl p-8 shadow-lg border border-slate-100\"\n            >\n              {/* Department Header */}\n              <div className=\"text-center mb-8\">\n                <div className={`w-16 h-16 bg-gradient-to-r ${department.color} rounded-full flex items-center justify-center mx-auto mb-4`}>\n                  <Briefcase className=\"h-8 w-8 text-white\" />\n                </div>\n                <h3 className=\"text-2xl md:text-3xl font-bold text-slate-900 mb-2\">\n                  {department.name}\n                </h3>\n                <div className=\"flex items-center justify-center text-slate-600\">\n                  <MapPin className=\"h-4 w-4 mr-2\" />\n                  <span>{department.location}</span>\n                </div>\n              </div>\n\n              {/* Team Members Grid */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n                {department.members.map((member, memberIndex) => (\n                  <motion.div\n                    key={member.name}\n                    initial={{ opacity: 0, y: 20 }}\n                    whileInView={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.6, delay: (deptIndex * 0.1) + (memberIndex * 0.05) }}\n                    viewport={{ once: true }}\n                    className=\"group\"\n                  >\n                    <div className=\"bg-gradient-to-br from-slate-50 to-blue-50 rounded-2xl p-6 border border-slate-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 text-center\">\n                      {/* Avatar */}\n                      <div className={`w-16 h-16 bg-gradient-to-r ${department.color} rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>\n                        <span className=\"text-white text-lg font-bold\">\n                          {member.name.split(' ').map(n => n[0]).join('')}\n                        </span>\n                      </div>\n\n                      {/* Member Info */}\n                      <h4 className=\"text-lg font-bold text-slate-900 group-hover:text-teal-600 transition-colors duration-300 mb-2\">\n                        {member.name}\n                      </h4>\n                      <p className=\"text-teal-600 font-semibold text-sm mb-2\">{member.role}</p>\n                      <p className=\"text-slate-500 text-xs\">{member.experience} experience</p>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"bg-gradient-to-r from-teal-600 to-blue-600 rounded-3xl p-12 text-white\">\n            <h3 className=\"text-3xl md:text-4xl font-bold mb-6\">\n              Join Our Team\n            </h3>\n            <p className=\"text-xl text-teal-100 mb-8 max-w-2xl mx-auto\">\n              We're always looking for talented individuals to join our growing team \n              across our global offices and diverse business verticals.\n            </p>\n            <button className=\"bg-white text-teal-600 hover:bg-teal-50 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300 shadow-lg\">\n              View Open Positions\n            </button>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKA,MAAM,cAAc;IAClB;QACE,MAAM;QACN,UAAU;QACV,SAAS;YACP;gBAAE,MAAM;gBAAmB,MAAM;gBAA6B,YAAY;YAAU;YACpF;gBAAE,MAAM;gBAAa,MAAM;gBAAqB,YAAY;YAAW;YACvE;gBAAE,MAAM;gBAAmB,MAAM;gBAAqB,YAAY;YAAW;YAC7E;gBAAE,MAAM;gBAAc,MAAM;gBAAwB,YAAY;YAAU;SAC3E;QACD,OAAO;IACT;IACA;QACE,MAAM;QACN,UAAU;QACV,SAAS;YACP;gBAAE,MAAM;gBAAa,MAAM;gBAAiC,YAAY;YAAW;YACnF;gBAAE,MAAM;gBAAkB,MAAM;gBAAkC,YAAY;YAAU;YACxF;gBAAE,MAAM;gBAAa,MAAM;gBAAyB,YAAY;YAAU;YAC1E;gBAAE,MAAM;gBAAgB,MAAM;gBAA4B,YAAY;YAAU;SACjF;QACD,OAAO;IACT;IACA;QACE,MAAM;QACN,UAAU;QACV,SAAS;YACP;gBAAE,MAAM;gBAAmB,MAAM;gBAA0B,YAAY;YAAW;YAClF;gBAAE,MAAM;gBAAgB,MAAM;gBAA2B,YAAY;YAAW;YAChF;gBAAE,MAAM;gBAAmB,MAAM;gBAAyB,YAAY;YAAU;YAChF;gBAAE,MAAM;gBAAkB,MAAM;gBAA4B,YAAY;YAAU;SACnF;QACD,OAAO;IACT;IACA;QACE,MAAM;QACN,UAAU;QACV,SAAS;YACP;gBAAE,MAAM;gBAAkB,MAAM;gBAA4B,YAAY;YAAW;YACnF;gBAAE,MAAM;gBAAkB,MAAM;gBAAwB,YAAY;YAAW;YAC/E;gBAAE,MAAM;gBAAoB,MAAM;gBAAiC,YAAY;YAAW;YAC1F;gBAAE,MAAM;gBAAkB,MAAM;gBAA8B,YAAY;YAAU;SACrF;QACD,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;;gCAAqD;8CAC7D,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAEtC,8OAAC;4BAAE,WAAU;sCAA2D;;;;;;;;;;;;8BAM1E,8OAAC;oBAAI,WAAU;8BACZ,YAAY,GAAG,CAAC,CAAC,YAAY,0BAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,YAAY;4BAAI;4BACpD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,2BAA2B,EAAE,WAAW,KAAK,CAAC,2DAA2D,CAAC;sDACzH,cAAA,8OAAC,4MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC;4CAAG,WAAU;sDACX,WAAW,IAAI;;;;;;sDAElB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAM,WAAW,QAAQ;;;;;;;;;;;;;;;;;;8CAK9B,8OAAC;oCAAI,WAAU;8CACZ,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BAC/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,AAAC,YAAY,MAAQ,cAAc;4CAAM;4CAC7E,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAW,CAAC,2BAA2B,EAAE,WAAW,KAAK,CAAC,mHAAmH,CAAC;kEACjL,cAAA,8OAAC;4DAAK,WAAU;sEACb,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;kEAKhD,8OAAC;wDAAG,WAAU;kEACX,OAAO,IAAI;;;;;;kEAEd,8OAAC;wDAAE,WAAU;kEAA4C,OAAO,IAAI;;;;;;kEACpE,8OAAC;wDAAE,WAAU;;4DAA0B,OAAO,UAAU;4DAAC;;;;;;;;;;;;;2CApBtD,OAAO,IAAI;;;;;;;;;;;2BAzBjB,WAAW,IAAI;;;;;;;;;;8BAuD1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAI5D,8OAAC;gCAAO,WAAU;0CAA8H;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5J", "debugId": null}}, {"offset": {"line": 1175, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/sections/about/leadership.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Linkedin, Mail, Award, GraduationCap } from \"lucide-react\";\n\nconst leaders = [\n  {\n    name: \"<PERSON>\",\n    position: \"Chief Executive Officer & Managing Partner\",\n    image: \"/api/placeholder/300/400\",\n    bio: \"<PERSON> brings over 20 years of investment banking and private equity experience, having led over $15B in transactions across technology and healthcare sectors.\",\n    education: \"MBA Harvard Business School, BS Stanford University\",\n    achievements: [\"Forbes 40 Under 40\", \"PE Professional of the Year 2023\"],\n    linkedin: \"#\",\n    email: \"<EMAIL>\"\n  },\n  {\n    name: \"<PERSON>\",\n    position: \"Chief Investment Officer\",\n    image: \"/api/placeholder/300/400\",\n    bio: \"<PERSON> oversees KleverCo's investment strategy with expertise in emerging markets and sustainable investing, managing our $12.5B portfolio.\",\n    education: \"PhD Economics MIT, MBA Wharton\",\n    achievements: [\"CFA Institute Recognition\", \"Sustainable Investor Award\"],\n    linkedin: \"#\",\n    email: \"<EMAIL>\"\n  },\n  {\n    name: \"<PERSON>. <PERSON>\",\n    position: \"Head of Healthcare Investments\",\n    image: \"/api/placeholder/300/400\",\n    bio: \"<PERSON> leads our healthcare portfolio with deep expertise in biotechnology and medical devices, having founded two successful biotech companies.\",\n    education: \"<PERSON>, PhD Bioengineering\",\n    achievements: [\"Healthcare Innovation Award\", \"Biotech Entrepreneur of the Year\"],\n    linkedin: \"#\",\n    email: \"<EMAIL>\"\n  },\n  {\n    name: \"James Thompson\",\n    position: \"Head of Technology Investments\",\n    image: \"/api/placeholder/300/400\",\n    bio: \"James drives our technology investment strategy, specializing in AI, cybersecurity, and fintech with over 15 years in Silicon Valley.\",\n    education: \"MS Computer Science Stanford, BS MIT\",\n    achievements: [\"Tech Investor of the Year\", \"AI Innovation Recognition\"],\n    linkedin: \"#\",\n    email: \"<EMAIL>\"\n  }\n];\n\nexport function Leadership() {\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-slate-50 to-blue-50\">\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold text-slate-900 mb-6\">\n            Leadership <span className=\"text-teal-500\">Team</span>\n          </h2>\n          <p className=\"text-xl text-slate-600 max-w-3xl mx-auto\">\n            Meet the visionary leaders driving KleverCo's success through decades of combined \n            experience, strategic insight, and unwavering commitment to excellence.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-12\">\n          {leaders.map((leader, index) => (\n            <motion.div\n              key={leader.name}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"group\"\n            >\n              <div className=\"bg-white rounded-3xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2\">\n                {/* Profile Image */}\n                <div className=\"relative mb-6\">\n                  <div className=\"w-32 h-32 mx-auto rounded-full bg-gradient-to-r from-teal-400 to-blue-400 p-1\">\n                    <div className=\"w-full h-full rounded-full bg-slate-200 flex items-center justify-center\">\n                      <span className=\"text-2xl font-bold text-slate-600\">\n                        {leader.name.split(' ').map(n => n[0]).join('')}\n                      </span>\n                    </div>\n                  </div>\n                  <div className=\"absolute -bottom-2 left-1/2 transform -translate-x-1/2 bg-teal-500 text-white px-3 py-1 rounded-full text-sm font-semibold\">\n                    Leader\n                  </div>\n                </div>\n\n                {/* Basic Info */}\n                <div className=\"text-center mb-6\">\n                  <h3 className=\"text-2xl font-bold text-slate-900 mb-2\">{leader.name}</h3>\n                  <p className=\"text-teal-600 font-semibold mb-4\">{leader.position}</p>\n                  <p className=\"text-slate-600 leading-relaxed\">{leader.bio}</p>\n                </div>\n\n                {/* Education */}\n                <div className=\"mb-6\">\n                  <div className=\"flex items-center mb-2\">\n                    <GraduationCap className=\"h-5 w-5 text-teal-500 mr-2\" />\n                    <span className=\"font-semibold text-slate-900\">Education</span>\n                  </div>\n                  <p className=\"text-slate-600 text-sm\">{leader.education}</p>\n                </div>\n\n                {/* Achievements */}\n                <div className=\"mb-6\">\n                  <div className=\"flex items-center mb-2\">\n                    <Award className=\"h-5 w-5 text-teal-500 mr-2\" />\n                    <span className=\"font-semibold text-slate-900\">Recognition</span>\n                  </div>\n                  <div className=\"space-y-1\">\n                    {leader.achievements.map((achievement, idx) => (\n                      <div key={idx} className=\"flex items-center\">\n                        <div className=\"w-2 h-2 bg-teal-500 rounded-full mr-2\"></div>\n                        <span className=\"text-slate-600 text-sm\">{achievement}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Contact */}\n                <div className=\"flex justify-center space-x-4 pt-4 border-t border-slate-100\">\n                  <a\n                    href={leader.linkedin}\n                    className=\"w-10 h-10 bg-blue-600 hover:bg-blue-700 rounded-full flex items-center justify-center transition-colors duration-300\"\n                  >\n                    <Linkedin className=\"h-5 w-5 text-white\" />\n                  </a>\n                  <a\n                    href={`mailto:${leader.email}`}\n                    className=\"w-10 h-10 bg-teal-600 hover:bg-teal-700 rounded-full flex items-center justify-center transition-colors duration-300\"\n                  >\n                    <Mail className=\"h-5 w-5 text-white\" />\n                  </a>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Team Stats */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"mt-20\"\n        >\n          <div className=\"bg-gradient-to-r from-slate-900 to-blue-900 rounded-3xl p-12 text-white\">\n            <div className=\"text-center mb-12\">\n              <h3 className=\"text-3xl font-bold mb-4\">Our Team Excellence</h3>\n              <p className=\"text-slate-300 text-lg\">\n                Collective expertise that drives exceptional results\n              </p>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8 text-center\">\n              <div>\n                <div className=\"text-4xl font-bold text-teal-400 mb-2\">150+</div>\n                <div className=\"text-slate-300\">Team Members</div>\n              </div>\n              <div>\n                <div className=\"text-4xl font-bold text-teal-400 mb-2\">500+</div>\n                <div className=\"text-slate-300\">Years Combined Experience</div>\n              </div>\n              <div>\n                <div className=\"text-4xl font-bold text-teal-400 mb-2\">45+</div>\n                <div className=\"text-slate-300\">Countries Represented</div>\n              </div>\n              <div>\n                <div className=\"text-4xl font-bold text-teal-400 mb-2\">25+</div>\n                <div className=\"text-slate-300\">Industry Awards</div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,UAAU;IACd;QACE,MAAM;QACN,UAAU;QACV,OAAO;QACP,KAAK;QACL,WAAW;QACX,cAAc;YAAC;YAAsB;SAAmC;QACxE,UAAU;QACV,OAAO;IACT;IACA;QACE,MAAM;QACN,UAAU;QACV,OAAO;QACP,KAAK;QACL,WAAW;QACX,cAAc;YAAC;YAA6B;SAA6B;QACzE,UAAU;QACV,OAAO;IACT;IACA;QACE,MAAM;QACN,UAAU;QACV,OAAO;QACP,KAAK;QACL,WAAW;QACX,cAAc;YAAC;YAA+B;SAAmC;QACjF,UAAU;QACV,OAAO;IACT;IACA;QACE,MAAM;QACN,UAAU;QACV,OAAO;QACP,KAAK;QACL,WAAW;QACX,cAAc;YAAC;YAA6B;SAA4B;QACxE,UAAU;QACV,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;;gCAAqD;8CACtD,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAE7C,8OAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;;8BAM1D,8OAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEACb,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;;;;;0DAIlD,8OAAC;gDAAI,WAAU;0DAA6H;;;;;;;;;;;;kDAM9I,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA0C,OAAO,IAAI;;;;;;0DACnE,8OAAC;gDAAE,WAAU;0DAAoC,OAAO,QAAQ;;;;;;0DAChE,8OAAC;gDAAE,WAAU;0DAAkC,OAAO,GAAG;;;;;;;;;;;;kDAI3D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;kEACzB,8OAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;;0DAEjD,8OAAC;gDAAE,WAAU;0DAA0B,OAAO,SAAS;;;;;;;;;;;;kDAIzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;;0DAEjD,8OAAC;gDAAI,WAAU;0DACZ,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,oBACrC,8OAAC;wDAAc,WAAU;;0EACvB,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAA0B;;;;;;;uDAFlC;;;;;;;;;;;;;;;;kDAShB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAM,OAAO,QAAQ;gDACrB,WAAU;0DAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,8OAAC;gDACC,MAAM,CAAC,OAAO,EAAE,OAAO,KAAK,EAAE;gDAC9B,WAAU;0DAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2BAlEjB,OAAO,IAAI;;;;;;;;;;8BA2EtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;0CAKxC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAAwC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DAAiB;;;;;;;;;;;;kDAElC,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAAwC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DAAiB;;;;;;;;;;;;kDAElC,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAAwC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DAAiB;;;;;;;;;;;;kDAElC,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAAwC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}, {"offset": {"line": 1730, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/navigation/footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { TrendingUp, Mail, Phone, MapPin, Linkedin, Twitter, Facebook } from \"lucide-react\";\n\nconst footerSections = [\n  {\n    title: \"Company\",\n    links: [\n      { name: \"About Us\", href: \"#about\" },\n      { name: \"Our Team\", href: \"#team\" },\n      { name: \"Careers\", href: \"#careers\" },\n      { name: \"News\", href: \"#news\" },\n    ]\n  },\n  {\n    title: \"Investment\",\n    links: [\n      { name: \"Sectors\", href: \"#sectors\" },\n      { name: \"Portfolio\", href: \"#portfolio\" },\n      { name: \"Process\", href: \"#process\" },\n      { name: \"ESG\", href: \"#esg\" },\n    ]\n  },\n  {\n    title: \"Resources\",\n    links: [\n      { name: \"Research\", href: \"#research\" },\n      { name: \"Reports\", href: \"#reports\" },\n      { name: \"Insights\", href: \"#insights\" },\n      { name: \"Events\", href: \"#events\" },\n    ]\n  }\n];\n\nconst socialLinks = [\n  { icon: Linkedin, href: \"#\", label: \"LinkedIn\" },\n  { icon: Twitter, href: \"#\", label: \"Twitter\" },\n  { icon: Facebook, href: \"#\", label: \"Facebook\" },\n];\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-slate-900 text-white\">\n      <div className=\"container mx-auto px-6 py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"mb-6\"\n            >\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <TrendingUp className=\"h-8 w-8 text-teal-400\" />\n                <span className=\"text-2xl font-bold\">\n                  Klever<span className=\"text-teal-400\">Co</span>\n                </span>\n              </div>\n              <p className=\"text-slate-300 leading-relaxed mb-6\">\n                Leading investment company specializing in strategic partnerships across \n                Healthcare, Technology, Energy, and Agriculture sectors. Building tomorrow's \n                success stories today.\n              </p>\n            </motion.div>\n\n            {/* Contact Info */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.1 }}\n              viewport={{ once: true }}\n              className=\"space-y-3\"\n            >\n              <div className=\"flex items-center space-x-3 text-slate-300\">\n                <MapPin className=\"h-5 w-5 text-teal-400\" />\n                <span>123 Investment Plaza, Financial District, NY 10004</span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-slate-300\">\n                <Phone className=\"h-5 w-5 text-teal-400\" />\n                <span>+****************</span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-slate-300\">\n                <Mail className=\"h-5 w-5 text-teal-400\" />\n                <span><EMAIL></span>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Footer Links */}\n          {footerSections.map((section, index) => (\n            <motion.div\n              key={section.title}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"text-lg font-semibold mb-4 text-white\">\n                {section.title}\n              </h3>\n              <ul className=\"space-y-3\">\n                {section.links.map((link) => (\n                  <li key={link.name}>\n                    <a\n                      href={link.href}\n                      className=\"text-slate-300 hover:text-teal-400 transition-colors duration-300\"\n                    >\n                      {link.name}\n                    </a>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.5 }}\n          viewport={{ once: true }}\n          className=\"border-t border-slate-700 mt-12 pt-8\"\n        >\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            {/* Copyright */}\n            <div className=\"text-slate-400 text-sm\">\n              © 2024 KleverCo Investment Company. All rights reserved.\n            </div>\n\n            {/* Social Links */}\n            <div className=\"flex items-center space-x-4\">\n              {socialLinks.map((social, index) => {\n                const Icon = social.icon;\n                return (\n                  <motion.a\n                    key={social.label}\n                    href={social.href}\n                    initial={{ opacity: 0, scale: 0 }}\n                    whileInView={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.3, delay: 0.6 + index * 0.1 }}\n                    viewport={{ once: true }}\n                    whileHover={{ scale: 1.1 }}\n                    className=\"w-10 h-10 bg-slate-800 rounded-full flex items-center justify-center text-slate-400 hover:text-teal-400 hover:bg-slate-700 transition-all duration-300\"\n                    aria-label={social.label}\n                  >\n                    <Icon className=\"h-5 w-5\" />\n                  </motion.a>\n                );\n              })}\n            </div>\n\n            {/* Legal Links */}\n            <div className=\"flex items-center space-x-6 text-sm text-slate-400\">\n              <a href=\"#privacy\" className=\"hover:text-teal-400 transition-colors duration-300\">\n                Privacy Policy\n              </a>\n              <a href=\"#terms\" className=\"hover:text-teal-400 transition-colors duration-300\">\n                Terms of Service\n              </a>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,iBAAiB;IACrB;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;YAAS;YACnC;gBAAE,MAAM;gBAAY,MAAM;YAAQ;YAClC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;SAC/B;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAO,MAAM;YAAO;SAC7B;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAU,MAAM;YAAU;SACnC;IACH;CACD;AAED,MAAM,cAAc;IAClB;QAAE,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAK,OAAO;IAAW;IAC/C;QAAE,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAK,OAAO;IAAU;IAC7C;QAAE,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAK,OAAO;IAAW;CAChD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;oDAAK,WAAU;;wDAAqB;sEAC7B,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAG1C,8OAAC;4CAAE,WAAU;sDAAsC;;;;;;;;;;;;8CAQrD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;wBAMX,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,MAAM,QAAQ;gCAAI;gCACtD,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK;;;;;;kDAEhB,8OAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC;0DACC,cAAA,8OAAC;oDACC,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;+BAXjB,QAAQ,KAAK;;;;;;;;;;;8BA0BxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CAAyB;;;;;;0CAKxC,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;oCACxB,MAAM,OAAO,OAAO,IAAI;oCACxB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCAEP,MAAM,OAAO,IAAI;wCACjB,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM,QAAQ;wCAAI;wCACtD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CAAE,OAAO;wCAAI;wCACzB,WAAU;wCACV,cAAY,OAAO,KAAK;kDAExB,cAAA,8OAAC;4CAAK,WAAU;;;;;;uCAVX,OAAO,KAAK;;;;;gCAavB;;;;;;0CAIF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAqD;;;;;;kDAGlF,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAAqD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9F", "debugId": null}}]}