"use client";

import { motion } from "framer-motion";
import { Fuel, Sun, Zap, Truck } from "lucide-react";

export function EnergyServices() {
  const services = [
    {
      title: "Oil & Gas",
      icon: Fuel,
      description: "We have long term significant expertise in the responsible sourcing of Oil and Gas. We have the logistical affiliates for moving it for customers globally in an efficient manner. We try our level best to adhere to sustainable practices as much realistically possible, while meeting global energy demands.",
      details: "We are engaged in few upstream negotiations, midstream with our affiliates and trade of derivatives with our source and clients base.",
      capabilities: [
        "Responsible sourcing of Oil and Gas",
        "Global logistics and transportation",
        "Upstream negotiations",
        "Midstream operations with affiliates",
        "Derivatives trading",
        "Sustainable practices implementation"
      ]
    },
    {
      title: "Renewable",
      icon: Sun,
      description: "We have started expanding alongside hydrocarbons towards renewable energy space in the likes of solar panels, solar powered geysers and EV power stations and stand alone units. In line with our trajectory towards climate conscious shift of energy sources.",
      details: "Our renewable energy portfolio focuses on sustainable solutions that meet the growing demand for clean energy while supporting global climate goals.",
      capabilities: [
        "Solar panel systems",
        "Solar powered geysers",
        "EV power stations",
        "Stand alone energy units",
        "Climate conscious solutions",
        "Clean energy transition support"
      ]
    }
  ];

  return (
    <section className="py-32 bg-white">
      <div className="container mx-auto px-6">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-8 tracking-tight">
              Energy <span className="font-normal text-slate-600">Solutions</span>
            </h2>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto mb-8"></div>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed font-light">
              Comprehensive energy services spanning traditional and renewable sources
            </p>
          </motion.div>

          {/* Services Grid */}
          <div className="space-y-32">
            {services.map((service, index) => {
              const Icon = service.icon;
              const isEven = index % 2 === 0;

              return (
                <motion.div
                  key={service.title}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                  viewport={{ once: true }}
                  className={`grid grid-cols-1 lg:grid-cols-2 gap-16 items-center ${
                    isEven ? "" : "lg:grid-flow-col-dense"
                  }`}
                >
                  {/* Content */}
                  <div className={`${isEven ? "" : "lg:col-start-2"}`}>
                    <div className="mb-8">
                      <div className="flex items-center mb-6">
                        <div className="bg-slate-900 p-4 rounded-2xl mr-6">
                          <Icon className="h-8 w-8 text-white" />
                        </div>
                        <h3 className="text-4xl md:text-5xl font-light text-slate-900 tracking-tight">
                          {service.title}
                        </h3>
                      </div>
                      <div className="w-24 h-px bg-gradient-to-r from-slate-300 to-transparent mb-8"></div>
                    </div>

                    <div className="space-y-6">
                      <p className="text-lg text-slate-700 leading-relaxed font-light">
                        {service.description}
                      </p>
                      <p className="text-lg text-slate-700 leading-relaxed font-light">
                        {service.details}
                      </p>
                    </div>

                    {/* Capabilities */}
                    <div className="mt-12">
                      <h4 className="text-xl font-medium text-slate-900 mb-6">Key Capabilities</h4>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        {service.capabilities.map((capability, idx) => (
                          <div key={idx} className="flex items-center">
                            <div className="w-2 h-2 bg-slate-900 rounded-full mr-3"></div>
                            <span className="text-slate-700 font-light">{capability}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Visual Element */}
                  <div className={`${isEven ? "" : "lg:col-start-1"}`}>
                    <div className="bg-slate-50 rounded-2xl p-12 border border-slate-100">
                      <div className="text-center">
                        <div className="bg-white p-8 rounded-2xl mb-8 shadow-sm">
                          <Icon className="h-24 w-24 text-slate-600 mx-auto" />
                        </div>
                        <h4 className="text-2xl font-medium text-slate-900 mb-4">
                          {service.title}
                        </h4>
                        <p className="text-slate-600 font-light">
                          {service.title === "Oil & Gas" 
                            ? "Traditional energy solutions with sustainable practices"
                            : "Clean energy solutions for a sustainable future"
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
