import { Navbar } from "@/components/navigation/navbar";
import { Footer } from "@/components/navigation/footer";
import { ContactHero } from "@/components/sections/contact/contact-hero";
import { ContactForm } from "@/components/sections/contact/contact-form";
import { OfficeLocations } from "@/components/sections/contact/office-locations";

export const metadata = {
  title: "Contact - KleverCo Global Investment & Research Boutique",
  description: "Get in touch with KleverCo. Contact our global offices in USA, Malaysia, and Thailand for investment, business development, and manufacturing inquiries.",
};

export default function ContactPage() {
  return (
    <main className="min-h-screen">
      <Navbar />
      <ContactHero />
      <OfficeLocations />
      <ContactForm />
      <Footer />
    </main>
  );
}
