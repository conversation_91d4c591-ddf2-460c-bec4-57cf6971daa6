"use client";

import { motion } from "framer-motion";
import { Code, Database, Globe, Cpu, Shield, Zap } from "lucide-react";

export function ITCompanies() {
  const companies = [
    {
      name: "Oratier",
      subtitle: "Oratier Technologies",
      description: "Oratier Technologies is a software house over two decades and counting. It prides in automating Government Departments, Business Process Re-engineering and Developing Business Solutions which address the present needs of customers and provide scalability for future growth.",
      details: "Our Software Architecture team is composed of highly experienced professionals who design software to perfection applying best practices in Software, Design & Development. The Development, Configuration Management & Quality Assurance Departments work in close coordination so that the software is reliable, scalable, robust and usable.",
      icon: Code,
      features: [
        "Government Department Automation",
        "Business Process Re-engineering", 
        "Custom Business Solutions",
        "Software Architecture Design",
        "Quality Assurance & Testing"
      ]
    },
    {
      name: "NGT",
      subtitle: "N Generations Technologies",
      tagline: "Future in Present",
      description: "N Generations Technologies is a Private Limited Company which operates both within public and private sector through its subsidiaries along with its collaboration with its affiliates globally.",
      details: "At N Generations Technologies we believe in operating digitally using technology to transform experience of users to their real time needs.",
      icon: Globe,
      features: [
        "Digital Transformation",
        "Real-time User Experience",
        "Public & Private Sector Solutions",
        "Global Affiliate Network",
        "Technology Innovation"
      ]
    }
  ];

  return (
    <section className="py-32 bg-slate-50">
      <div className="container mx-auto px-6">
        <div className="max-w-7xl mx-auto">
          {/* Companies Grid */}
          <div className="space-y-32">
            {companies.map((company, index) => {
              const Icon = company.icon;
              const isEven = index % 2 === 0;

              return (
                <motion.div
                  key={company.name}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                  viewport={{ once: true }}
                  className={`grid grid-cols-1 lg:grid-cols-2 gap-16 items-center ${
                    isEven ? "" : "lg:grid-flow-col-dense"
                  }`}
                >
                  {/* Content */}
                  <div className={`${isEven ? "" : "lg:col-start-2"}`}>
                    <div className="mb-8">
                      <div className="flex items-center mb-6">
                        <div className="bg-slate-900 p-4 rounded-2xl mr-6">
                          <Icon className="h-8 w-8 text-white" />
                        </div>
                        <div>
                          <h2 className="text-4xl md:text-5xl font-light text-slate-900 tracking-tight">
                            {company.name}
                          </h2>
                          <p className="text-xl text-slate-600 font-light mt-2">
                            {company.subtitle}
                          </p>
                          {company.tagline && (
                            <p className="text-lg text-slate-500 italic mt-1">
                              {company.tagline}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="w-24 h-px bg-gradient-to-r from-slate-300 to-transparent mb-8"></div>
                    </div>

                    <div className="space-y-6">
                      <p className="text-lg text-slate-700 leading-relaxed font-light">
                        {company.description}
                      </p>
                      <p className="text-lg text-slate-700 leading-relaxed font-light">
                        {company.details}
                      </p>
                    </div>

                    {/* Features */}
                    <div className="mt-12">
                      <h3 className="text-xl font-medium text-slate-900 mb-6">Key Capabilities</h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        {company.features.map((feature, idx) => (
                          <div key={idx} className="flex items-center">
                            <div className="w-2 h-2 bg-slate-900 rounded-full mr-3"></div>
                            <span className="text-slate-700 font-light">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Visual Element */}
                  <div className={`${isEven ? "" : "lg:col-start-1"}`}>
                    <div className="bg-white rounded-2xl p-12 shadow-lg border border-slate-100">
                      <div className="text-center">
                        <div className="bg-slate-100 p-8 rounded-2xl mb-8">
                          <Icon className="h-24 w-24 text-slate-600 mx-auto" />
                        </div>
                        <h3 className="text-2xl font-medium text-slate-900 mb-4">
                          {company.name}
                        </h3>
                        <p className="text-slate-600 font-light">
                          {company.subtitle}
                        </p>
                        {company.tagline && (
                          <p className="text-slate-500 italic mt-2">
                            {company.tagline}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
