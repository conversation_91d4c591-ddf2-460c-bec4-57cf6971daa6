"use client";

import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON>Up, BarChart3, PieChart, Target, Globe, Shield } from "lucide-react";

const investmentCapabilities = [
  {
    title: "Global Investment Research",
    description: "Comprehensive market research and analysis across international markets, providing deep insights for strategic investment decisions.",
    icon: Globe,
    color: "from-blue-500 to-indigo-500"
  },
  {
    title: "Portfolio Management",
    description: "Professional portfolio management services with focus on risk optimization and sustainable returns across diverse asset classes.",
    icon: Pie<PERSON>hart,
    color: "from-green-500 to-emerald-500"
  },
  {
    title: "Strategic Advisory",
    description: "Expert strategic advisory services for complex investment decisions, mergers, acquisitions, and business development initiatives.",
    icon: Target,
    color: "from-purple-500 to-violet-500"
  },
  {
    title: "Risk Management",
    description: "Advanced risk assessment and management frameworks ensuring prudent investment strategies and capital preservation.",
    icon: Shield,
    color: "from-red-500 to-pink-500"
  }
];

export function InvestmentExpertise() {
  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
            Investment <span className="text-teal-500">Expertise</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            As a global investment and research boutique firm, we deliver sophisticated 
            investment solutions backed by deep market expertise and rigorous analysis.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {investmentCapabilities.map((capability, index) => {
            const Icon = capability.icon;
            
            return (
              <motion.div
                key={capability.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="bg-white rounded-3xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 h-full">
                  <div className="flex items-start mb-6">
                    <div className={`w-16 h-16 bg-gradient-to-r ${capability.color} rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-slate-900 group-hover:text-teal-600 transition-colors duration-300 mb-3">
                        {capability.title}
                      </h3>
                      <p className="text-slate-600 leading-relaxed">
                        {capability.description}
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Investment Approach */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="bg-white rounded-3xl p-12 shadow-lg border border-slate-100"
        >
          <div className="text-center mb-12">
            <TrendingUp className="h-16 w-16 text-teal-500 mx-auto mb-6" />
            <h3 className="text-3xl md:text-4xl font-bold text-slate-900 mb-6">
              Our Investment Approach
            </h3>
            <p className="text-xl text-slate-700 max-w-2xl mx-auto">
              We combine rigorous research, strategic thinking, and global market insights 
              to deliver exceptional investment outcomes for our clients.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <BarChart3 className="h-8 w-8 text-white" />
              </div>
              <h4 className="text-xl font-bold text-slate-900 mb-3">Research-Driven</h4>
              <p className="text-slate-600">
                Deep market analysis and comprehensive research form the foundation of our investment decisions.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Target className="h-8 w-8 text-white" />
              </div>
              <h4 className="text-xl font-bold text-slate-900 mb-3">Strategic Focus</h4>
              <p className="text-slate-600">
                Strategic alignment with long-term objectives and sustainable value creation principles.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-violet-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Globe className="h-8 w-8 text-white" />
              </div>
              <h4 className="text-xl font-bold text-slate-900 mb-3">Global Perspective</h4>
              <p className="text-slate-600">
                International market expertise with local insights across multiple geographic regions.
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
