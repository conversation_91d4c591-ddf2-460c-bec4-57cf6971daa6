"use client";

import { motion } from "framer-motion";
import { Calendar, TrendingUp, Building, Globe2 } from "lucide-react";

const milestones = [
  {
    year: "1999",
    title: "Foundation",
    description: "KleverCo was founded with a vision to revolutionize strategic investment through innovative partnerships.",
    icon: Building,
    color: "from-blue-500 to-indigo-500"
  },
  {
    year: "2005",
    title: "Global Expansion",
    description: "Expanded operations internationally, establishing offices in key financial markets across three continents.",
    icon: Globe2,
    color: "from-teal-500 to-cyan-500"
  },
  {
    year: "2012",
    title: "Sector Diversification",
    description: "Diversified portfolio across Healthcare, Technology, Energy, and Agriculture sectors with strategic focus.",
    icon: TrendingUp,
    color: "from-green-500 to-emerald-500"
  },
  {
    year: "2018",
    title: "Digital Innovation",
    description: "Pioneered AI-driven investment analytics and digital transformation initiatives across portfolio companies.",
    icon: Calendar,
    color: "from-purple-500 to-violet-500"
  },
  {
    year: "2024",
    title: "Sustainable Future",
    description: "Leading ESG-focused investments and sustainable development initiatives for long-term value creation.",
    icon: TrendingUp,
    color: "from-orange-500 to-red-500"
  }
];

export function CompanyStory() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
            Our <span className="text-teal-500">Journey</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            From humble beginnings to global leadership, our story is one of unwavering commitment 
            to excellence, innovation, and creating lasting value for our partners.
          </p>
        </motion.div>

        {/* Timeline */}
        <div className="relative">
          {/* Timeline Line */}
          <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-teal-500 to-blue-500 rounded-full hidden md:block"></div>

          <div className="space-y-16">
            {milestones.map((milestone, index) => {
              const Icon = milestone.icon;
              const isEven = index % 2 === 0;
              
              return (
                <motion.div
                  key={milestone.year}
                  initial={{ opacity: 0, x: isEven ? -50 : 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className={`flex items-center ${isEven ? 'md:flex-row' : 'md:flex-row-reverse'} flex-col md:space-x-8`}
                >
                  {/* Content */}
                  <div className={`flex-1 ${isEven ? 'md:text-right' : 'md:text-left'} text-center md:mb-0 mb-8`}>
                    <div className="bg-white rounded-2xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-shadow duration-300">
                      <div className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r ${milestone.color} rounded-full mb-6 mx-auto ${isEven ? 'md:ml-auto md:mr-0' : 'md:mr-auto md:ml-0'}`}>
                        <Icon className="h-8 w-8 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-slate-900 mb-2">{milestone.title}</h3>
                      <p className="text-slate-600 leading-relaxed">{milestone.description}</p>
                    </div>
                  </div>

                  {/* Timeline Node */}
                  <div className="relative z-10 hidden md:block">
                    <div className="w-6 h-6 bg-white border-4 border-teal-500 rounded-full shadow-lg"></div>
                  </div>

                  {/* Year */}
                  <div className={`flex-1 ${isEven ? 'md:text-left' : 'md:text-right'} text-center`}>
                    <div className="bg-slate-900 text-white rounded-2xl p-6 inline-block">
                      <h4 className="text-3xl font-bold">{milestone.year}</h4>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>

        {/* Bottom Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
          className="text-center mt-20"
        >
          <div className="bg-gradient-to-r from-teal-50 to-blue-50 rounded-2xl p-12 border border-teal-100">
            <h3 className="text-3xl font-bold text-slate-900 mb-6">
              Building Tomorrow, Today
            </h3>
            <p className="text-xl text-slate-700 mb-8 max-w-2xl mx-auto">
              Our journey continues as we shape the future of investment excellence, 
              one strategic partnership at a time.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-teal-600 hover:bg-teal-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-300">
                View Our Portfolio
              </button>
              <button className="border-2 border-teal-600 text-teal-600 hover:bg-teal-600 hover:text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300">
                Meet Our Team
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
