"use client";

import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { Video, Database, Globe, Smartphone, Building } from "lucide-react";

export function HealthcareOverview() {
  const healthcareServices = [
    {
      title: "Telemedicine",
      description: "Healthcare services provided remotely using telecommunications technology, enabling healthcare professionals to evaluate, diagnose, and treat patients without in-person visits.",
      icon: Video,
      href: "/businesses/healthcare/telemedicine",
      stats: "50+ million patients annually",
      color: "blue"
    },
    {
      title: "HIMS",
      description: "Hospital Information Management System - Complete ERP solution featuring Sparrow EMR for healthcare automation and clinical data management.",
      icon: Database,
      href: "/businesses/healthcare/hims",
      stats: "250+ million EMR records",
      color: "emerald"
    },
    {
      title: "WHOA",
      description: "World Health One Application - Comprehensive healthcare app addressing diagnostics to electronic medical records with supply chain management.",
      icon: Smartphone,
      href: "/businesses/healthcare/whoa",
      stats: "Global compliance standards",
      color: "purple"
    },
    {
      title: "Dentistry",
      description: "Aesthetica Dental Clinique - Boutique dental clinics delivering finest private dental care across three Surrey, England locations.",
      icon: Building,
      href: "/businesses/healthcare/dentistry",
      stats: "3 Surrey locations",
      color: "cyan"
    },
    {
      title: "GLOVES",
      description: "State-of-the-art PPE nitrile glove manufacturing with vertically integrated business model and global distribution capabilities.",
      icon: Globe,
      href: "/businesses/healthcare/gloves",
      stats: "Vertically integrated manufacturing",
      color: "orange"
    }
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: "bg-blue-500 hover:bg-blue-600 from-blue-100 to-blue-200",
      emerald: "bg-emerald-500 hover:bg-emerald-600 from-emerald-100 to-emerald-200",
      purple: "bg-purple-500 hover:bg-purple-600 from-purple-100 to-purple-200",
      cyan: "bg-cyan-500 hover:bg-cyan-600 from-cyan-100 to-cyan-200",
      orange: "bg-orange-500 hover:bg-orange-600 from-orange-100 to-orange-200"
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <>
      {/* Hero Section */}
      <section className="relative pt-32 pb-20 bg-gradient-to-b from-slate-900 to-slate-800 overflow-hidden">
        {/* Subtle background pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,.1)_50%,transparent_75%,transparent_100%)] bg-[length:20px_20px]"></div>
        </div>
        
        <div className="container mx-auto px-6 relative z-10">
          <div className="max-w-6xl mx-auto">
            {/* Company Logo */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <div className="flex justify-center mb-8">
                <Image
                  src="/images/logo-klever.png"
                  alt="KleverCo Logo"
                  width={200}
                  height={80}
                  className="h-20 w-auto"
                />
              </div>
            </motion.div>

            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-20"
            >
              <h1 className="text-5xl md:text-7xl font-light text-white mb-8 tracking-tight">
                HEALTHCARE
              </h1>
              <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-400 to-transparent mx-auto mb-8"></div>
              <p className="text-2xl text-slate-400 max-w-4xl mx-auto leading-relaxed font-light">
                Comprehensive healthcare solutions spanning telemedicine, manufacturing, and specialized services
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Services Navigation */}
      <section className="py-32 bg-white">
        <div className="container mx-auto px-6">
          <div className="max-w-7xl mx-auto">
            {/* Section Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-20"
            >
              <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-8 tracking-tight">
                Healthcare <span className="font-normal text-slate-600">Services</span>
              </h2>
              <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto mb-8"></div>
              <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed font-light">
                Explore our comprehensive healthcare divisions and specialized services
              </p>
            </motion.div>

            {/* Services Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {healthcareServices.map((service, index) => {
                const Icon = service.icon;
                const colorClasses = getColorClasses(service.color);
                const [bgColor, hoverColor] = colorClasses.split(' ');
                
                return (
                  <motion.div
                    key={service.title}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <Link href={service.href}>
                      <div className="bg-white rounded-2xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300 group cursor-pointer h-full">
                        {/* Icon */}
                        <div className={`${bgColor} ${hoverColor} p-4 rounded-xl w-fit mb-6 transition-all duration-300`}>
                          <Icon className="h-8 w-8 text-white" />
                        </div>

                        {/* Content */}
                        <div className="mb-6">
                          <h3 className="text-2xl font-medium text-slate-900 mb-4 group-hover:text-slate-700 transition-colors">
                            {service.title}
                          </h3>
                          <div className="w-16 h-px bg-slate-300 mb-4"></div>
                          <p className="text-slate-600 leading-relaxed font-light mb-4">
                            {service.description}
                          </p>
                        </div>

                        {/* Stats */}
                        <div className="bg-slate-50 rounded-lg p-4 mb-6">
                          <p className="text-slate-700 font-medium text-sm">
                            {service.stats}
                          </p>
                        </div>

                        {/* Call to Action */}
                        <div className="flex items-center text-slate-600 group-hover:text-slate-900 transition-colors">
                          <span className="font-medium">Learn More</span>
                          <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </div>
                      </div>
                    </Link>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* Healthcare at a Glance */}
      <section className="py-32 bg-slate-50">
        <div className="container mx-auto px-6">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-20"
            >
              <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-8 tracking-tight">
                Healthcare at a <span className="font-normal text-slate-600">Glance</span>
              </h2>
              <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto"></div>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                { value: "50+ million", label: "Patients treated annually via Telemedicine" },
                { value: "250+ million", label: "EMR records with unlimited scalability" },
                { value: "50+ Hospitals", label: "Tertiary hospitals providing HIMS treatments" },
                { value: "3 Countries", label: "Global manufacturing and distribution presence" }
              ].map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-2xl p-8 shadow-lg border border-slate-100 text-center"
                >
                  <div className="text-3xl md:text-4xl font-light text-slate-900 mb-4 tracking-tight">
                    {stat.value}
                  </div>
                  <p className="text-slate-600 font-light leading-relaxed">
                    {stat.label}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
