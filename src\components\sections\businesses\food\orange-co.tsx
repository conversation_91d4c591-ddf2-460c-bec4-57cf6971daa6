"use client";

import { motion } from "framer-motion";
import { ChefHat, Utensils, Heart, Star, MapPin, Clock } from "lucide-react";

export function OrangeCo() {
  const features = [
    {
      title: "Fresh Ingredients",
      description: "Sourced daily for maximum freshness and quality",
      icon: Heart
    },
    {
      title: "Authentic Recipes",
      description: "Traditional south-asian flavors with modern presentation",
      icon: ChefHat
    },
    {
      title: "Quality Service",
      description: "Exceptional dining experience with attention to detail",
      icon: Star
    },
    {
      title: "Local Presence",
      description: "Bringing authentic cuisine to your neighborhood",
      icon: MapPin
    }
  ];

  const highlights = [
    "Fresh south-asian cuisine",
    "Exceptional taste experiences", 
    "Quality ingredients sourced daily",
    "Traditional recipes with modern touch",
    "Warm and welcoming atmosphere",
    "Community-focused dining"
  ];

  return (
    <section className="py-32 bg-white">
      <div className="container mx-auto px-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-8 tracking-tight">
              The Orange <span className="font-normal text-slate-600">Co</span>
            </h2>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto mb-8"></div>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed font-light">
              Bringing you fresh south-asian cuisine with exceptional taste experiences
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
            {/* Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="space-y-8">
                <div>
                  <h3 className="text-3xl font-light text-slate-900 mb-6 tracking-tight">
                    Authentic <span className="font-normal text-slate-600">Flavors</span>
                  </h3>
                  <p className="text-lg text-slate-700 leading-relaxed font-light">
                    The Orange Co is dedicated to bringing you the finest south-asian cuisine, crafted with passion and served with pride. Our commitment to quality and authenticity ensures every meal is a memorable experience.
                  </p>
                </div>

                <div>
                  <h4 className="text-xl font-medium text-slate-900 mb-4">What Makes Us Special</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {highlights.map((highlight, index) => (
                      <div key={index} className="flex items-center">
                        <div className="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
                        <span className="text-slate-700 font-light">{highlight}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-slate-50 rounded-2xl p-8 border border-slate-100">
                  <div className="flex items-center mb-4">
                    <Clock className="h-6 w-6 text-orange-500 mr-3" />
                    <h4 className="text-lg font-medium text-slate-900">Fresh Daily</h4>
                  </div>
                  <p className="text-slate-600 font-light">
                    Every dish is prepared fresh daily using the finest ingredients, ensuring the authentic taste and quality that defines The Orange Co experience.
                  </p>
                </div>
              </div>
            </motion.div>

            {/* Visual Element */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="bg-gradient-to-br from-orange-100 to-orange-200 rounded-2xl p-12 text-center">
                <div className="bg-white p-8 rounded-2xl mb-8 shadow-sm">
                  <Utensils className="h-24 w-24 text-orange-500 mx-auto" />
                </div>
                <h3 className="text-2xl font-medium text-slate-900 mb-4">
                  The Orange Co
                </h3>
                <p className="text-slate-600 font-light mb-6">
                  Fresh South-Asian Cuisine
                </p>
                <div className="flex justify-center space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-orange-400 fill-current" />
                  ))}
                </div>
                <p className="text-sm text-slate-500 mt-2">Exceptional taste experiences</p>
              </div>
            </motion.div>
          </div>

          {/* Features Grid */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h3 className="text-3xl font-light text-slate-900 text-center mb-16 tracking-tight">
              Our <span className="font-normal text-slate-600">Commitment</span>
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {features.map((feature, index) => {
                const Icon = feature.icon;
                
                return (
                  <motion.div
                    key={feature.title}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="bg-slate-50 rounded-2xl p-8 text-center border border-slate-100 hover:shadow-lg transition-all duration-300"
                  >
                    <div className="bg-orange-500 p-4 rounded-2xl w-fit mx-auto mb-6">
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <h4 className="text-xl font-medium text-slate-900 mb-4">
                      {feature.title}
                    </h4>
                    <p className="text-slate-600 font-light leading-relaxed">
                      {feature.description}
                    </p>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
