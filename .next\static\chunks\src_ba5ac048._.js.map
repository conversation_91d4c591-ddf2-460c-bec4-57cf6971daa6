{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/navigation/navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { Button } from \"@/components/ui/button\";\nimport { TrendingUp, Menu, X } from \"lucide-react\";\n\nconst navItems = [\n  { name: \"Home\", href: \"#home\" },\n  { name: \"Sectors\", href: \"#sectors\" },\n  { name: \"About\", href: \"#about\" },\n  { name: \"Portfolio\", href: \"#portfolio\" },\n  { name: \"Contact\", href: \"#contact\" },\n];\n\nexport function Navbar() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  return (\n    <motion.nav\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.8 }}\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        isScrolled \n          ? \"bg-white/95 backdrop-blur-md shadow-lg border-b border-slate-200/50\" \n          : \"bg-transparent\"\n      }`}\n    >\n      <div className=\"container mx-auto px-6\">\n        <div className=\"flex items-center justify-between h-16 md:h-20\">\n          {/* Logo */}\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            className=\"flex items-center space-x-2 cursor-pointer\"\n          >\n            <TrendingUp className={`h-8 w-8 ${isScrolled ? \"text-teal-600\" : \"text-teal-400\"}`} />\n            <span className={`text-2xl font-bold ${isScrolled ? \"text-slate-900\" : \"text-white\"}`}>\n              Klever<span className={isScrolled ? \"text-teal-600\" : \"text-teal-400\"}>Co</span>\n            </span>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item, index) => (\n              <motion.a\n                key={item.name}\n                href={item.href}\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className={`font-medium transition-colors duration-300 hover:text-teal-500 ${\n                  isScrolled ? \"text-slate-700\" : \"text-white\"\n                }`}\n              >\n                {item.name}\n              </motion.a>\n            ))}\n          </div>\n\n          {/* Desktop CTA */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button\n              variant={isScrolled ? \"outline\" : \"secondary\"}\n              className={`transition-all duration-300 ${\n                isScrolled \n                  ? \"border-teal-600 text-teal-600 hover:bg-teal-600 hover:text-white\" \n                  : \"bg-white/20 text-white border-white/30 hover:bg-white hover:text-slate-900\"\n              }`}\n            >\n              Get Started\n            </Button>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className={`md:hidden p-2 rounded-lg transition-colors duration-300 ${\n              isScrolled ? \"text-slate-700 hover:bg-slate-100\" : \"text-white hover:bg-white/20\"\n            }`}\n          >\n            {isMobileMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      <AnimatePresence>\n        {isMobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"md:hidden bg-white/95 backdrop-blur-md border-t border-slate-200/50\"\n          >\n            <div className=\"container mx-auto px-6 py-4\">\n              <div className=\"flex flex-col space-y-4\">\n                {navItems.map((item, index) => (\n                  <motion.a\n                    key={item.name}\n                    href={item.href}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ duration: 0.3, delay: index * 0.1 }}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    className=\"text-slate-700 font-medium py-2 hover:text-teal-600 transition-colors duration-300\"\n                  >\n                    {item.name}\n                  </motion.a>\n                ))}\n                <motion.div\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.3, delay: navItems.length * 0.1 }}\n                  className=\"pt-4 border-t border-slate-200\"\n                >\n                  <Button className=\"w-full bg-teal-600 hover:bg-teal-700 text-white\">\n                    Get Started\n                  </Button>\n                </motion.div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;;;AALA;;;;;AAOA,MAAM,WAAW;IACf;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAa,MAAM;IAAa;IACxC;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAC,4DAA4D,EACtE,aACI,wEACA,kBACJ;;0BAEF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,WAAU;;8CAEV,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAW,CAAC,QAAQ,EAAE,aAAa,kBAAkB,iBAAiB;;;;;;8CAClF,6LAAC;oCAAK,WAAW,CAAC,mBAAmB,EAAE,aAAa,mBAAmB,cAAc;;wCAAE;sDAC/E,6LAAC;4CAAK,WAAW,aAAa,kBAAkB;sDAAiB;;;;;;;;;;;;;;;;;;sCAK3E,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,KAAK,IAAI;oCACf,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,WAAW,CAAC,+DAA+D,EACzE,aAAa,mBAAmB,cAChC;8CAED,KAAK,IAAI;mCATL,KAAK,IAAI;;;;;;;;;;sCAepB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,aAAa,YAAY;gCAClC,WAAW,CAAC,4BAA4B,EACtC,aACI,qEACA,8EACJ;0CACH;;;;;;;;;;;sCAMH,6LAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAW,CAAC,wDAAwD,EAClE,aAAa,sCAAsC,gCACnD;sCAED,iCAAmB,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMtE,6LAAC,4LAAA,CAAA,kBAAe;0BACb,kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCAEP,MAAM,KAAK,IAAI;wCACf,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,SAAS,IAAM,oBAAoB;wCACnC,WAAU;kDAET,KAAK,IAAI;uCARL,KAAK,IAAI;;;;;8CAWlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,SAAS,MAAM,GAAG;oCAAI;oCAC1D,WAAU;8CAEV,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;kDAAkD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtF;GA3HgB;KAAA", "debugId": null}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/sections/vertical-accordion.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { \n  Heart, \n  Cpu, \n  Zap, \n  Wheat,\n  Building2,\n  Plane,\n  ChevronRight\n} from \"lucide-react\";\n\nconst sectors = [\n  {\n    id: \"healthcare\",\n    name: \"Healthcare\",\n    icon: Heart,\n    description: \"Advancing medical innovation and patient care through strategic investments in biotechnology, pharmaceuticals, and healthcare services.\",\n    color: \"from-red-500 to-pink-500\",\n    bgColor: \"bg-red-50\",\n    stats: \"50+ Portfolio Companies\"\n  },\n  {\n    id: \"technology\",\n    name: \"Technology\",\n    icon: Cpu,\n    description: \"Driving digital transformation with investments in AI, cloud computing, cybersecurity, and emerging technologies.\",\n    color: \"from-blue-500 to-indigo-500\",\n    bgColor: \"bg-blue-50\",\n    stats: \"75+ Portfolio Companies\"\n  },\n  {\n    id: \"energy\",\n    name: \"Energy\",\n    icon: Zap,\n    description: \"Powering the future through renewable energy, clean technology, and sustainable energy infrastructure investments.\",\n    color: \"from-yellow-500 to-orange-500\",\n    bgColor: \"bg-yellow-50\",\n    stats: \"30+ Portfolio Companies\"\n  },\n  {\n    id: \"agriculture\",\n    name: \"Agriculture\",\n    icon: Wheat,\n    description: \"Revolutionizing food production with precision agriculture, sustainable farming, and agtech innovations.\",\n    color: \"from-green-500 to-emerald-500\",\n    bgColor: \"bg-green-50\",\n    stats: \"25+ Portfolio Companies\"\n  },\n  {\n    id: \"real-estate\",\n    name: \"Real Estate\",\n    icon: Building2,\n    description: \"Building tomorrow's communities through strategic real estate development and property technology investments.\",\n    color: \"from-purple-500 to-violet-500\",\n    bgColor: \"bg-purple-50\",\n    stats: \"40+ Portfolio Companies\"\n  },\n  {\n    id: \"aerospace\",\n    name: \"Aerospace\",\n    icon: Plane,\n    description: \"Reaching new heights with investments in space technology, aviation innovation, and defense systems.\",\n    color: \"from-teal-500 to-cyan-500\",\n    bgColor: \"bg-teal-50\",\n    stats: \"15+ Portfolio Companies\"\n  }\n];\n\nexport function VerticalAccordion() {\n  const [activePanel, setActivePanel] = useState<string | null>(\"healthcare\");\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold text-slate-900 mb-6\">\n            Investment <span className=\"text-teal-500\">Sectors</span>\n          </h2>\n          <p className=\"text-xl text-slate-600 max-w-3xl mx-auto\">\n            Diversified portfolio across high-growth sectors, driving innovation and creating value through strategic partnerships.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-6 gap-4 h-[600px]\">\n          {sectors.map((sector, index) => {\n            const Icon = sector.icon;\n            const isActive = activePanel === sector.id;\n            \n            return (\n              <motion.div\n                key={sector.id}\n                initial={{ opacity: 0, x: -20 }}\n                whileInView={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className={`relative cursor-pointer rounded-2xl overflow-hidden transition-all duration-500 ${\n                  isActive ? 'lg:col-span-3' : 'lg:col-span-1'\n                } ${isActive ? 'h-full' : 'h-full lg:h-full'}`}\n                onClick={() => setActivePanel(isActive ? null : sector.id)}\n              >\n                <div className={`h-full bg-gradient-to-br ${sector.color} relative overflow-hidden`}>\n                  {/* Background Pattern */}\n                  <div className=\"absolute inset-0 bg-black/10\"></div>\n                  \n                  {/* Content */}\n                  <div className=\"relative z-10 h-full flex flex-col justify-between p-6 text-white\">\n                    <div className=\"flex items-center justify-between\">\n                      <Icon className=\"h-8 w-8\" />\n                      <ChevronRight \n                        className={`h-6 w-6 transition-transform duration-300 ${\n                          isActive ? 'rotate-90' : ''\n                        }`} \n                      />\n                    </div>\n                    \n                    <div>\n                      <h3 className={`font-bold mb-2 transition-all duration-300 ${\n                        isActive ? 'text-2xl lg:text-3xl' : 'text-xl lg:text-lg'\n                      }`}>\n                        {sector.name}\n                      </h3>\n                      \n                      <AnimatePresence>\n                        {isActive && (\n                          <motion.div\n                            initial={{ opacity: 0, height: 0 }}\n                            animate={{ opacity: 1, height: \"auto\" }}\n                            exit={{ opacity: 0, height: 0 }}\n                            transition={{ duration: 0.3 }}\n                            className=\"overflow-hidden\"\n                          >\n                            <p className=\"text-white/90 mb-4 leading-relaxed\">\n                              {sector.description}\n                            </p>\n                            <div className=\"bg-white/20 backdrop-blur-sm rounded-lg p-4\">\n                              <p className=\"font-semibold text-sm\">\n                                {sector.stats}\n                              </p>\n                            </div>\n                          </motion.div>\n                        )}\n                      </AnimatePresence>\n                    </div>\n                  </div>\n                </div>\n              </motion.div>\n            );\n          })}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAcA,MAAM,UAAU;IACd;QACE,IAAI;QACJ,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;QACb,OAAO;QACP,SAAS;QACT,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,mMAAA,CAAA,MAAG;QACT,aAAa;QACb,OAAO;QACP,SAAS;QACT,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,mMAAA,CAAA,MAAG;QACT,aAAa;QACb,OAAO;QACP,SAAS;QACT,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;QACb,OAAO;QACP,SAAS;QACT,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,mNAAA,CAAA,YAAS;QACf,aAAa;QACb,OAAO;QACP,SAAS;QACT,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;QACb,OAAO;QACP,SAAS;QACT,OAAO;IACT;CACD;AAEM,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;;gCAAqD;8CACtD,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAE7C,6LAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;;8BAK1D,6LAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ;wBACpB,MAAM,OAAO,OAAO,IAAI;wBACxB,MAAM,WAAW,gBAAgB,OAAO,EAAE;wBAE1C,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAW,CAAC,gFAAgF,EAC1F,WAAW,kBAAkB,gBAC9B,CAAC,EAAE,WAAW,WAAW,oBAAoB;4BAC9C,SAAS,IAAM,eAAe,WAAW,OAAO,OAAO,EAAE;sCAEzD,cAAA,6LAAC;gCAAI,WAAW,CAAC,yBAAyB,EAAE,OAAO,KAAK,CAAC,yBAAyB,CAAC;;kDAEjF,6LAAC;wCAAI,WAAU;;;;;;kDAGf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;;;;;;kEAChB,6LAAC,yNAAA,CAAA,eAAY;wDACX,WAAW,CAAC,0CAA0C,EACpD,WAAW,cAAc,IACzB;;;;;;;;;;;;0DAIN,6LAAC;;kEACC,6LAAC;wDAAG,WAAW,CAAC,2CAA2C,EACzD,WAAW,yBAAyB,sBACpC;kEACC,OAAO,IAAI;;;;;;kEAGd,6LAAC,4LAAA,CAAA,kBAAe;kEACb,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,SAAS;gEAAE,SAAS;gEAAG,QAAQ;4DAAE;4DACjC,SAAS;gEAAE,SAAS;gEAAG,QAAQ;4DAAO;4DACtC,MAAM;gEAAE,SAAS;gEAAG,QAAQ;4DAAE;4DAC9B,YAAY;gEAAE,UAAU;4DAAI;4DAC5B,WAAU;;8EAEV,6LAAC;oEAAE,WAAU;8EACV,OAAO,WAAW;;;;;;8EAErB,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;kFACV,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA9CxB,OAAO,EAAE;;;;;oBAyDpB;;;;;;;;;;;;;;;;;AAKV;GA1FgB;KAAA", "debugId": null}}, {"offset": {"line": 698, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/navigation/footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { TrendingUp, Mail, Phone, MapPin, Linkedin, Twitter, Facebook } from \"lucide-react\";\n\nconst footerSections = [\n  {\n    title: \"Company\",\n    links: [\n      { name: \"About Us\", href: \"#about\" },\n      { name: \"Our Team\", href: \"#team\" },\n      { name: \"Careers\", href: \"#careers\" },\n      { name: \"News\", href: \"#news\" },\n    ]\n  },\n  {\n    title: \"Investment\",\n    links: [\n      { name: \"Sectors\", href: \"#sectors\" },\n      { name: \"Portfolio\", href: \"#portfolio\" },\n      { name: \"Process\", href: \"#process\" },\n      { name: \"ESG\", href: \"#esg\" },\n    ]\n  },\n  {\n    title: \"Resources\",\n    links: [\n      { name: \"Research\", href: \"#research\" },\n      { name: \"Reports\", href: \"#reports\" },\n      { name: \"Insights\", href: \"#insights\" },\n      { name: \"Events\", href: \"#events\" },\n    ]\n  }\n];\n\nconst socialLinks = [\n  { icon: Linkedin, href: \"#\", label: \"LinkedIn\" },\n  { icon: Twitter, href: \"#\", label: \"Twitter\" },\n  { icon: Facebook, href: \"#\", label: \"Facebook\" },\n];\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-slate-900 text-white\">\n      <div className=\"container mx-auto px-6 py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"mb-6\"\n            >\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <TrendingUp className=\"h-8 w-8 text-teal-400\" />\n                <span className=\"text-2xl font-bold\">\n                  Klever<span className=\"text-teal-400\">Co</span>\n                </span>\n              </div>\n              <p className=\"text-slate-300 leading-relaxed mb-6\">\n                Leading investment company specializing in strategic partnerships across \n                Healthcare, Technology, Energy, and Agriculture sectors. Building tomorrow's \n                success stories today.\n              </p>\n            </motion.div>\n\n            {/* Contact Info */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.1 }}\n              viewport={{ once: true }}\n              className=\"space-y-3\"\n            >\n              <div className=\"flex items-center space-x-3 text-slate-300\">\n                <MapPin className=\"h-5 w-5 text-teal-400\" />\n                <span>123 Investment Plaza, Financial District, NY 10004</span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-slate-300\">\n                <Phone className=\"h-5 w-5 text-teal-400\" />\n                <span>+****************</span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-slate-300\">\n                <Mail className=\"h-5 w-5 text-teal-400\" />\n                <span><EMAIL></span>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Footer Links */}\n          {footerSections.map((section, index) => (\n            <motion.div\n              key={section.title}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"text-lg font-semibold mb-4 text-white\">\n                {section.title}\n              </h3>\n              <ul className=\"space-y-3\">\n                {section.links.map((link) => (\n                  <li key={link.name}>\n                    <a\n                      href={link.href}\n                      className=\"text-slate-300 hover:text-teal-400 transition-colors duration-300\"\n                    >\n                      {link.name}\n                    </a>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.5 }}\n          viewport={{ once: true }}\n          className=\"border-t border-slate-700 mt-12 pt-8\"\n        >\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            {/* Copyright */}\n            <div className=\"text-slate-400 text-sm\">\n              © 2024 KleverCo Investment Company. All rights reserved.\n            </div>\n\n            {/* Social Links */}\n            <div className=\"flex items-center space-x-4\">\n              {socialLinks.map((social, index) => {\n                const Icon = social.icon;\n                return (\n                  <motion.a\n                    key={social.label}\n                    href={social.href}\n                    initial={{ opacity: 0, scale: 0 }}\n                    whileInView={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.3, delay: 0.6 + index * 0.1 }}\n                    viewport={{ once: true }}\n                    whileHover={{ scale: 1.1 }}\n                    className=\"w-10 h-10 bg-slate-800 rounded-full flex items-center justify-center text-slate-400 hover:text-teal-400 hover:bg-slate-700 transition-all duration-300\"\n                    aria-label={social.label}\n                  >\n                    <Icon className=\"h-5 w-5\" />\n                  </motion.a>\n                );\n              })}\n            </div>\n\n            {/* Legal Links */}\n            <div className=\"flex items-center space-x-6 text-sm text-slate-400\">\n              <a href=\"#privacy\" className=\"hover:text-teal-400 transition-colors duration-300\">\n                Privacy Policy\n              </a>\n              <a href=\"#terms\" className=\"hover:text-teal-400 transition-colors duration-300\">\n                Terms of Service\n              </a>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,iBAAiB;IACrB;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;YAAS;YACnC;gBAAE,MAAM;gBAAY,MAAM;YAAQ;YAClC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;SAC/B;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAO,MAAM;YAAO;SAC7B;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAU,MAAM;YAAU;SACnC;IACH;CACD;AAED,MAAM,cAAc;IAClB;QAAE,MAAM,6MAAA,CAAA,WAAQ;QAAE,MAAM;QAAK,OAAO;IAAW;IAC/C;QAAE,MAAM,2MAAA,CAAA,UAAO;QAAE,MAAM;QAAK,OAAO;IAAU;IAC7C;QAAE,MAAM,6MAAA,CAAA,WAAQ;QAAE,MAAM;QAAK,OAAO;IAAW;CAChD;AAEM,SAAS;IACd,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC;oDAAK,WAAU;;wDAAqB;sEAC7B,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAG1C,6LAAC;4CAAE,WAAU;sDAAsC;;;;;;;;;;;;8CAQrD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;wBAMX,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,MAAM,QAAQ;gCAAI;gCACtD,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,6LAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK;;;;;;kDAEhB,6LAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,6LAAC;0DACC,cAAA,6LAAC;oDACC,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;+BAXjB,QAAQ,KAAK;;;;;;;;;;;8BA0BxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CAAyB;;;;;;0CAKxC,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;oCACxB,MAAM,OAAO,OAAO,IAAI;oCACxB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCAEP,MAAM,OAAO,IAAI;wCACjB,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM,QAAQ;wCAAI;wCACtD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CAAE,OAAO;wCAAI;wCACzB,WAAU;wCACV,cAAY,OAAO,KAAK;kDAExB,cAAA,6LAAC;4CAAK,WAAU;;;;;;uCAVX,OAAO,KAAK;;;;;gCAavB;;;;;;0CAIF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAqD;;;;;;kDAGlF,6LAAC;wCAAE,MAAK;wCAAS,WAAU;kDAAqD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9F;KAhIgB", "debugId": null}}]}