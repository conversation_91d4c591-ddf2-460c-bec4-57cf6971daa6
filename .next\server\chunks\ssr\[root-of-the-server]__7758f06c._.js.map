{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/navigation/navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { Button } from \"@/components/ui/button\";\nimport { TrendingUp, Menu, X } from \"lucide-react\";\n\nconst navItems = [\n  { name: \"Home\", href: \"/\" },\n  { name: \"About\", href: \"/about\" },\n  { name: \"Businesses\", href: \"/businesses\" },\n  { name: \"Contact\", href: \"/contact\" },\n];\n\nexport function Navbar() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  return (\n    <motion.nav\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.8 }}\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        isScrolled \n          ? \"bg-white/95 backdrop-blur-md shadow-lg border-b border-slate-200/50\" \n          : \"bg-transparent\"\n      }`}\n    >\n      <div className=\"container mx-auto px-6\">\n        <div className=\"flex items-center justify-between h-16 md:h-20\">\n          {/* Logo */}\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            className=\"flex items-center space-x-2 cursor-pointer\"\n          >\n            <TrendingUp className={`h-8 w-8 ${isScrolled ? \"text-teal-600\" : \"text-teal-400\"}`} />\n            <span className={`text-2xl font-bold ${isScrolled ? \"text-slate-900\" : \"text-white\"}`}>\n              Klever<span className={isScrolled ? \"text-teal-600\" : \"text-teal-400\"}>Co</span>\n            </span>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item, index) => (\n              <motion.div\n                key={item.name}\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n              >\n                <Link\n                  href={item.href}\n                  className={`font-medium transition-colors duration-300 hover:text-teal-500 ${\n                    isScrolled ? \"text-slate-700\" : \"text-white\"\n                  }`}\n                >\n                  {item.name}\n                </Link>\n              </motion.div>\n            ))}\n          </div>\n\n          {/* Desktop CTA */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button\n              variant={isScrolled ? \"outline\" : \"secondary\"}\n              className={`transition-all duration-300 ${\n                isScrolled \n                  ? \"border-teal-600 text-teal-600 hover:bg-teal-600 hover:text-white\" \n                  : \"bg-white/20 text-white border-white/30 hover:bg-white hover:text-slate-900\"\n              }`}\n            >\n              Get Started\n            </Button>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className={`md:hidden p-2 rounded-lg transition-colors duration-300 ${\n              isScrolled ? \"text-slate-700 hover:bg-slate-100\" : \"text-white hover:bg-white/20\"\n            }`}\n          >\n            {isMobileMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      <AnimatePresence>\n        {isMobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"md:hidden bg-white/95 backdrop-blur-md border-t border-slate-200/50\"\n          >\n            <div className=\"container mx-auto px-6 py-4\">\n              <div className=\"flex flex-col space-y-4\">\n                {navItems.map((item, index) => (\n                  <motion.div\n                    key={item.name}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ duration: 0.3, delay: index * 0.1 }}\n                  >\n                    <Link\n                      href={item.href}\n                      onClick={() => setIsMobileMenuOpen(false)}\n                      className=\"text-slate-700 font-medium py-2 hover:text-teal-600 transition-colors duration-300 block\"\n                    >\n                      {item.name}\n                    </Link>\n                  </motion.div>\n                ))}\n                <motion.div\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.3, delay: navItems.length * 0.1 }}\n                  className=\"pt-4 border-t border-slate-200\"\n                >\n                  <Button className=\"w-full bg-teal-600 hover:bg-teal-700 text-white\">\n                    Get Started\n                  </Button>\n                </motion.div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AANA;;;;;;;AAQA,MAAM,WAAW;IACf;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAc,MAAM;IAAc;IAC1C;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAC,4DAA4D,EACtE,aACI,wEACA,kBACJ;;0BAEF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,WAAU;;8CAEV,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAW,CAAC,QAAQ,EAAE,aAAa,kBAAkB,iBAAiB;;;;;;8CAClF,8OAAC;oCAAK,WAAW,CAAC,mBAAmB,EAAE,aAAa,mBAAmB,cAAc;;wCAAE;sDAC/E,8OAAC;4CAAK,WAAW,aAAa,kBAAkB;sDAAiB;;;;;;;;;;;;;;;;;;sCAK3E,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;8CAEhD,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,+DAA+D,EACzE,aAAa,mBAAmB,cAChC;kDAED,KAAK,IAAI;;;;;;mCAXP,KAAK,IAAI;;;;;;;;;;sCAkBpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,aAAa,YAAY;gCAClC,WAAW,CAAC,4BAA4B,EACtC,aACI,qEACA,8EACJ;0CACH;;;;;;;;;;;sCAMH,8OAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAW,CAAC,wDAAwD,EAClE,aAAa,sCAAsC,gCACnD;sCAED,iCAAmB,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMtE,8OAAC,yLAAA,CAAA,kBAAe;0BACb,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;kDAEhD,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,oBAAoB;4CACnC,WAAU;sDAET,KAAK,IAAI;;;;;;uCAVP,KAAK,IAAI;;;;;8CAclB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,SAAS,MAAM,GAAG;oCAAI;oCAC1D,WAAU;8CAEV,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;kDAAkD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtF", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/navigation/footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { TrendingUp, Mail, Phone, MapPin, Linkedin, Twitter, Facebook } from \"lucide-react\";\n\nconst footerSections = [\n  {\n    title: \"Company\",\n    links: [\n      { name: \"About Us\", href: \"#about\" },\n      { name: \"Our Team\", href: \"#team\" },\n      { name: \"Careers\", href: \"#careers\" },\n      { name: \"News\", href: \"#news\" },\n    ]\n  },\n  {\n    title: \"Investment\",\n    links: [\n      { name: \"Sectors\", href: \"#sectors\" },\n      { name: \"Portfolio\", href: \"#portfolio\" },\n      { name: \"Process\", href: \"#process\" },\n      { name: \"ESG\", href: \"#esg\" },\n    ]\n  },\n  {\n    title: \"Resources\",\n    links: [\n      { name: \"Research\", href: \"#research\" },\n      { name: \"Reports\", href: \"#reports\" },\n      { name: \"Insights\", href: \"#insights\" },\n      { name: \"Events\", href: \"#events\" },\n    ]\n  }\n];\n\nconst socialLinks = [\n  { icon: Linkedin, href: \"#\", label: \"LinkedIn\" },\n  { icon: Twitter, href: \"#\", label: \"Twitter\" },\n  { icon: Facebook, href: \"#\", label: \"Facebook\" },\n];\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-slate-900 text-white\">\n      <div className=\"container mx-auto px-6 py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"mb-6\"\n            >\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <TrendingUp className=\"h-8 w-8 text-teal-400\" />\n                <span className=\"text-2xl font-bold\">\n                  Klever<span className=\"text-teal-400\">Co</span>\n                </span>\n              </div>\n              <p className=\"text-slate-300 leading-relaxed mb-6\">\n                Leading investment company specializing in strategic partnerships across \n                Healthcare, Technology, Energy, and Agriculture sectors. Building tomorrow's \n                success stories today.\n              </p>\n            </motion.div>\n\n            {/* Contact Info */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.1 }}\n              viewport={{ once: true }}\n              className=\"space-y-3\"\n            >\n              <div className=\"flex items-center space-x-3 text-slate-300\">\n                <MapPin className=\"h-5 w-5 text-teal-400\" />\n                <span>123 Investment Plaza, Financial District, NY 10004</span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-slate-300\">\n                <Phone className=\"h-5 w-5 text-teal-400\" />\n                <span>+****************</span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-slate-300\">\n                <Mail className=\"h-5 w-5 text-teal-400\" />\n                <span><EMAIL></span>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Footer Links */}\n          {footerSections.map((section, index) => (\n            <motion.div\n              key={section.title}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"text-lg font-semibold mb-4 text-white\">\n                {section.title}\n              </h3>\n              <ul className=\"space-y-3\">\n                {section.links.map((link) => (\n                  <li key={link.name}>\n                    <a\n                      href={link.href}\n                      className=\"text-slate-300 hover:text-teal-400 transition-colors duration-300\"\n                    >\n                      {link.name}\n                    </a>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.5 }}\n          viewport={{ once: true }}\n          className=\"border-t border-slate-700 mt-12 pt-8\"\n        >\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            {/* Copyright */}\n            <div className=\"text-slate-400 text-sm\">\n              © 2024 KleverCo Investment Company. All rights reserved.\n            </div>\n\n            {/* Social Links */}\n            <div className=\"flex items-center space-x-4\">\n              {socialLinks.map((social, index) => {\n                const Icon = social.icon;\n                return (\n                  <motion.a\n                    key={social.label}\n                    href={social.href}\n                    initial={{ opacity: 0, scale: 0 }}\n                    whileInView={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.3, delay: 0.6 + index * 0.1 }}\n                    viewport={{ once: true }}\n                    whileHover={{ scale: 1.1 }}\n                    className=\"w-10 h-10 bg-slate-800 rounded-full flex items-center justify-center text-slate-400 hover:text-teal-400 hover:bg-slate-700 transition-all duration-300\"\n                    aria-label={social.label}\n                  >\n                    <Icon className=\"h-5 w-5\" />\n                  </motion.a>\n                );\n              })}\n            </div>\n\n            {/* Legal Links */}\n            <div className=\"flex items-center space-x-6 text-sm text-slate-400\">\n              <a href=\"#privacy\" className=\"hover:text-teal-400 transition-colors duration-300\">\n                Privacy Policy\n              </a>\n              <a href=\"#terms\" className=\"hover:text-teal-400 transition-colors duration-300\">\n                Terms of Service\n              </a>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,iBAAiB;IACrB;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;YAAS;YACnC;gBAAE,MAAM;gBAAY,MAAM;YAAQ;YAClC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;SAC/B;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAO,MAAM;YAAO;SAC7B;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAU,MAAM;YAAU;SACnC;IACH;CACD;AAED,MAAM,cAAc;IAClB;QAAE,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAK,OAAO;IAAW;IAC/C;QAAE,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAK,OAAO;IAAU;IAC7C;QAAE,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAK,OAAO;IAAW;CAChD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;oDAAK,WAAU;;wDAAqB;sEAC7B,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAG1C,8OAAC;4CAAE,WAAU;sDAAsC;;;;;;;;;;;;8CAQrD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;wBAMX,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,MAAM,QAAQ;gCAAI;gCACtD,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK;;;;;;kDAEhB,8OAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC;0DACC,cAAA,8OAAC;oDACC,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;+BAXjB,QAAQ,KAAK;;;;;;;;;;;8BA0BxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CAAyB;;;;;;0CAKxC,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;oCACxB,MAAM,OAAO,OAAO,IAAI;oCACxB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCAEP,MAAM,OAAO,IAAI;wCACjB,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM,QAAQ;wCAAI;wCACtD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CAAE,OAAO;wCAAI;wCACzB,WAAU;wCACV,cAAY,OAAO,KAAK;kDAExB,cAAA,8OAAC;4CAAK,WAAU;;;;;;uCAVX,OAAO,KAAK;;;;;gCAavB;;;;;;0CAIF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAqD;;;;;;kDAGlF,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAAqD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9F", "debugId": null}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/sections/businesses/businesses-hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Building, TrendingUp, Users, Heart } from \"lucide-react\";\n\nexport function BusinessesHero() {\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-blue-900 to-teal-900 overflow-hidden pt-20\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.05%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20\"></div>\n      \n      {/* Floating Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <motion.div\n          animate={{ \n            y: [0, -20, 0],\n            rotate: [0, 5, 0]\n          }}\n          transition={{ \n            duration: 6,\n            repeat: Infinity,\n            ease: \"easeInOut\"\n          }}\n          className=\"absolute top-20 left-10 w-20 h-20 bg-teal-500/20 rounded-full blur-xl\"\n        />\n        <motion.div\n          animate={{ \n            y: [0, 30, 0],\n            rotate: [0, -5, 0]\n          }}\n          transition={{ \n            duration: 8,\n            repeat: Infinity,\n            ease: \"easeInOut\"\n          }}\n          className=\"absolute bottom-20 right-10 w-32 h-32 bg-blue-500/20 rounded-full blur-xl\"\n        />\n      </div>\n      \n      <div className=\"container mx-auto px-6 relative z-10\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          {/* Main Content */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"mb-12\"\n          >\n            <div className=\"flex items-center justify-center mb-6\">\n              <Building className=\"h-16 w-16 text-teal-400 mr-4\" />\n              <h1 className=\"text-5xl md:text-7xl font-bold text-white\">\n                Our <span className=\"text-teal-400\">Businesses</span>\n              </h1>\n            </div>\n            <div className=\"h-1 w-32 bg-gradient-to-r from-teal-400 to-blue-400 mx-auto rounded-full mb-8\"></div>\n          </motion.div>\n\n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"text-xl md:text-2xl text-slate-300 mb-12 leading-relaxed max-w-3xl mx-auto\"\n          >\n            A global investment and research boutique firm with state-of-the-art PPE nitrile glove \n            manufacturing capabilities, delivering comprehensive solutions across multiple business verticals.\n          </motion.p>\n\n          {/* Business Areas Grid */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16\"\n          >\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300\">\n              <TrendingUp className=\"h-12 w-12 text-teal-400 mx-auto mb-4\" />\n              <h3 className=\"text-xl font-bold text-white mb-2\">We Develop Business</h3>\n              <p className=\"text-slate-300 text-sm\">Facilitate Business Growth</p>\n            </div>\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300\">\n              <Building className=\"h-12 w-12 text-teal-400 mx-auto mb-4\" />\n              <h3 className=\"text-xl font-bold text-white mb-2\">We Support Business</h3>\n              <p className=\"text-slate-300 text-sm\">Business Innovation and Support</p>\n            </div>\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300\">\n              <Users className=\"h-12 w-12 text-teal-400 mx-auto mb-4\" />\n              <h3 className=\"text-xl font-bold text-white mb-2\">We Advise</h3>\n              <p className=\"text-slate-300 text-sm\">Expert Financial Advice</p>\n            </div>\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300\">\n              <Heart className=\"h-12 w-12 text-teal-400 mx-auto mb-4\" />\n              <h3 className=\"text-xl font-bold text-white mb-2\">We Serve Humanity</h3>\n              <p className=\"text-slate-300 text-sm\">HEALTH</p>\n            </div>\n          </motion.div>\n\n          {/* CTA */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center\"\n          >\n            <button className=\"bg-teal-500 hover:bg-teal-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300 shadow-lg hover:shadow-xl\">\n              Explore Our Services\n            </button>\n            <button className=\"border-2 border-white text-white hover:bg-white hover:text-teal-900 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300\">\n              Contact Our Team\n            </button>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 1, delay: 1 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <motion.div\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n          className=\"w-6 h-10 border-2 border-white rounded-full flex justify-center\"\n        >\n          <motion.div\n            animate={{ y: [0, 12, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n            className=\"w-1 h-3 bg-white rounded-full mt-2\"\n          />\n        </motion.div>\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BACP,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;4BACd,QAAQ;gCAAC;gCAAG;gCAAG;6BAAE;wBACnB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;wBACA,WAAU;;;;;;kCAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BACP,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;4BACb,QAAQ;gCAAC;gCAAG,CAAC;gCAAG;6BAAE;wBACpB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;wBACA,WAAU;;;;;;;;;;;;0BAId,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAG,WAAU;;gDAA4C;8DACpD,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;8CAGxC,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;sCAMD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;8CAExC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;8CAExC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;8CAExC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;sCAK1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAO,WAAU;8CAA+I;;;;;;8CAGjK,8OAAC;oCAAO,WAAU;8CAA6I;;;;;;;;;;;;;;;;;;;;;;;0BAQrK,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;oBAAG,OAAO;gBAAE;gBACpC,WAAU;0BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;oBAC5C,WAAU;8BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBAAC;wBACzB,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;wBAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMtB", "debugId": null}}, {"offset": {"line": 1298, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/sections/businesses/business-services.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { TrendingUp, Building, Users, Heart, ArrowRight } from \"lucide-react\";\n\nconst businessServices = [\n  {\n    title: \"We Develop Business\",\n    subtitle: \"Facilitate Business Growth\",\n    description: \"Comprehensive business development services focused on facilitating sustainable growth through strategic planning, market analysis, and operational optimization.\",\n    icon: TrendingUp,\n    color: \"from-blue-500 to-indigo-500\",\n    features: [\n      \"Strategic Business Planning\",\n      \"Market Entry Strategies\", \n      \"Growth Facilitation\",\n      \"Performance Optimization\"\n    ]\n  },\n  {\n    title: \"We Support Business\", \n    subtitle: \"Business Innovation and Support\",\n    description: \"Innovative business support solutions designed to enhance operational efficiency, drive innovation, and provide comprehensive support for business transformation.\",\n    icon: Building,\n    color: \"from-green-500 to-emerald-500\",\n    features: [\n      \"Innovation Consulting\",\n      \"Operational Support\",\n      \"Technology Integration\",\n      \"Process Improvement\"\n    ]\n  },\n  {\n    title: \"We Advise\",\n    subtitle: \"Expert Financial Advice\", \n    description: \"Professional financial advisory services providing expert guidance on investment strategies, financial planning, and strategic decision-making for optimal business outcomes.\",\n    icon: Users,\n    color: \"from-purple-500 to-violet-500\",\n    features: [\n      \"Investment Advisory\",\n      \"Financial Planning\",\n      \"Risk Assessment\",\n      \"Strategic Consulting\"\n    ]\n  },\n  {\n    title: \"We Serve Humanity\",\n    subtitle: \"HEALTH\",\n    description: \"Dedicated to serving humanity through health-focused initiatives, including state-of-the-art PPE manufacturing and healthcare solutions for global well-being.\",\n    icon: Heart,\n    color: \"from-red-500 to-pink-500\",\n    features: [\n      \"PPE Manufacturing\",\n      \"Healthcare Solutions\",\n      \"Quality Assurance\",\n      \"Global Distribution\"\n    ]\n  }\n];\n\nexport function BusinessServices() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold text-slate-900 mb-6\">\n            Our <span className=\"text-teal-500\">Business Services</span>\n          </h2>\n          <p className=\"text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed\">\n            Comprehensive solutions across four key business verticals, delivering value through \n            strategic expertise, innovation, and commitment to excellence.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {businessServices.map((service, index) => {\n            const Icon = service.icon;\n            \n            return (\n              <motion.div\n                key={service.title}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"group\"\n              >\n                <div className=\"bg-gradient-to-br from-slate-50 to-blue-50 rounded-3xl p-8 border border-slate-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 h-full\">\n                  {/* Header */}\n                  <div className=\"flex items-start mb-6\">\n                    <div className={`w-16 h-16 bg-gradient-to-r ${service.color} rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300`}>\n                      <Icon className=\"h-8 w-8 text-white\" />\n                    </div>\n                    <div className=\"flex-1\">\n                      <h3 className=\"text-2xl font-bold text-slate-900 group-hover:text-teal-600 transition-colors duration-300 mb-1\">\n                        {service.title}\n                      </h3>\n                      <p className=\"text-teal-600 font-semibold text-lg\">{service.subtitle}</p>\n                    </div>\n                  </div>\n\n                  {/* Description */}\n                  <p className=\"text-slate-600 leading-relaxed mb-6\">\n                    {service.description}\n                  </p>\n\n                  {/* Features */}\n                  <div className=\"space-y-3 mb-6\">\n                    {service.features.map((feature, idx) => (\n                      <div key={idx} className=\"flex items-center\">\n                        <div className={`w-2 h-2 bg-gradient-to-r ${service.color} rounded-full mr-3`}></div>\n                        <span className=\"text-slate-700 font-medium\">{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n\n                  {/* CTA */}\n                  <button className=\"group/btn w-full py-3 rounded-lg font-semibold transition-all duration-300 bg-white border-2 border-slate-200 hover:border-teal-500 text-slate-700 hover:text-teal-600 flex items-center justify-center\">\n                    Learn More\n                    <ArrowRight className=\"h-5 w-5 ml-2 group-hover/btn:translate-x-1 transition-transform duration-300\" />\n                  </button>\n                </div>\n              </motion.div>\n            );\n          })}\n        </div>\n\n        {/* Bottom CTA Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"mt-20\"\n        >\n          <div className=\"bg-gradient-to-r from-teal-600 to-blue-600 rounded-3xl p-12 text-white text-center\">\n            <h3 className=\"text-3xl md:text-4xl font-bold mb-6\">\n              Ready to Partner with KleverCo?\n            </h3>\n            <p className=\"text-xl text-teal-100 mb-8 max-w-2xl mx-auto\">\n              Discover how our comprehensive business services can drive growth, \n              innovation, and success for your organization.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"bg-white text-teal-600 hover:bg-teal-50 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300 shadow-lg\">\n                Schedule Consultation\n              </button>\n              <button className=\"border-2 border-white text-white hover:bg-white hover:text-teal-600 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300\">\n                View Case Studies\n              </button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,mBAAmB;IACvB;QACE,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM,kNAAA,CAAA,aAAU;QAChB,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;;gCAAqD;8CAC7D,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAEtC,8OAAC;4BAAE,WAAU;sCAA2D;;;;;;;;;;;;8BAM1E,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS;wBAC9B,MAAM,OAAO,QAAQ,IAAI;wBAEzB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,2BAA2B,EAAE,QAAQ,KAAK,CAAC,2GAA2G,CAAC;0DACtK,cAAA,8OAAC;oDAAK,WAAU;;;;;;;;;;;0DAElB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAEhB,8OAAC;wDAAE,WAAU;kEAAuC,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;kDAKxE,8OAAC;wCAAE,WAAU;kDACV,QAAQ,WAAW;;;;;;kDAItB,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC9B,8OAAC;gDAAc,WAAU;;kEACvB,8OAAC;wDAAI,WAAW,CAAC,yBAAyB,EAAE,QAAQ,KAAK,CAAC,kBAAkB,CAAC;;;;;;kEAC7E,8OAAC;wDAAK,WAAU;kEAA8B;;;;;;;+CAFtC;;;;;;;;;;kDAQd,8OAAC;wCAAO,WAAU;;4CAA0M;0DAE1N,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;2BAvCrB,QAAQ,KAAK;;;;;oBA4CxB;;;;;;8BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAI5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAA8H;;;;;;kDAGhJ,8OAAC;wCAAO,WAAU;kDAA6I;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7K", "debugId": null}}, {"offset": {"line": 1656, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/sections/businesses/manufacturing-capabilities.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Shield, Factory, Award, Globe, CheckCircle, Heart } from \"lucide-react\";\n\nconst manufacturingFeatures = [\n  {\n    title: \"State-of-the-Art Facilities\",\n    description: \"Modern manufacturing facilities equipped with advanced technology for high-quality PPE production.\",\n    icon: Factory,\n    color: \"from-blue-500 to-indigo-500\"\n  },\n  {\n    title: \"Quality Assurance\",\n    description: \"Rigorous quality control processes ensuring all products meet international safety standards.\",\n    icon: Award,\n    color: \"from-green-500 to-emerald-500\"\n  },\n  {\n    title: \"Global Distribution\",\n    description: \"Comprehensive distribution network serving healthcare providers worldwide with reliable supply chains.\",\n    icon: Globe,\n    color: \"from-purple-500 to-violet-500\"\n  },\n  {\n    title: \"Safety Standards\",\n    description: \"Commitment to highest safety standards and regulatory compliance across all manufacturing processes.\",\n    icon: Shield,\n    color: \"from-red-500 to-pink-500\"\n  }\n];\n\nconst qualityStandards = [\n  \"ISO 13485 Medical Device Quality Management\",\n  \"FDA Approved Manufacturing Processes\",\n  \"CE Marking Compliance\",\n  \"ASTM International Standards\",\n  \"EN 455 European Standards\",\n  \"ANSI/AAMI Standards\"\n];\n\nexport function ManufacturingCapabilities() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold text-slate-900 mb-6\">\n            Manufacturing <span className=\"text-teal-500\">Capabilities</span>\n          </h2>\n          <p className=\"text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed\">\n            State-of-the-art PPE nitrile glove manufacturing with production facilities \n            ensuring highest quality standards for global healthcare needs.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-16\">\n          {manufacturingFeatures.map((feature, index) => {\n            const Icon = feature.icon;\n            \n            return (\n              <motion.div\n                key={feature.title}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"group\"\n              >\n                <div className=\"bg-gradient-to-br from-slate-50 to-blue-50 rounded-3xl p-8 border border-slate-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 h-full\">\n                  <div className=\"flex items-start mb-6\">\n                    <div className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300`}>\n                      <Icon className=\"h-8 w-8 text-white\" />\n                    </div>\n                    <div className=\"flex-1\">\n                      <h3 className=\"text-2xl font-bold text-slate-900 group-hover:text-teal-600 transition-colors duration-300 mb-3\">\n                        {feature.title}\n                      </h3>\n                      <p className=\"text-slate-600 leading-relaxed\">\n                        {feature.description}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </motion.div>\n            );\n          })}\n        </div>\n\n        {/* Manufacturing Hub */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"bg-gradient-to-r from-teal-600 to-blue-600 rounded-3xl p-12 text-white mb-16\"\n        >\n          <div className=\"text-center mb-8\">\n            <Factory className=\"h-16 w-16 text-teal-200 mx-auto mb-6\" />\n            <h3 className=\"text-3xl md:text-4xl font-bold mb-6\">\n              KLEVER Thai Hua Gloves\n            </h3>\n            <p className=\"text-xl text-teal-100 max-w-2xl mx-auto\">\n              Our dedicated manufacturing hub in Bangkok, Thailand, specializing in \n              high-quality nitrile glove production for global healthcare markets.\n            </p>\n          </div>\n          \n          <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center\">\n            <div className=\"text-teal-200 mb-2\">Manufacturing Location</div>\n            <div className=\"text-xl font-bold mb-1\">238/10 Ratchada-Pisek Road</div>\n            <div className=\"text-teal-200\">Huai-Khwang, Bangkok 10310, Thailand</div>\n          </div>\n        </motion.div>\n\n        {/* Quality Standards */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          viewport={{ once: true }}\n          className=\"bg-white rounded-3xl p-12 shadow-lg border border-slate-100\"\n        >\n          <div className=\"text-center mb-12\">\n            <Award className=\"h-16 w-16 text-teal-500 mx-auto mb-6\" />\n            <h3 className=\"text-3xl md:text-4xl font-bold text-slate-900 mb-6\">\n              Quality & Compliance Standards\n            </h3>\n            <p className=\"text-xl text-slate-700 max-w-2xl mx-auto\">\n              Our manufacturing processes adhere to the highest international quality \n              and safety standards, ensuring reliable protection for healthcare professionals.\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {qualityStandards.map((standard, index) => (\n              <motion.div\n                key={standard}\n                initial={{ opacity: 0, x: -20 }}\n                whileInView={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"flex items-center bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4\"\n              >\n                <CheckCircle className=\"h-6 w-6 text-green-500 mr-3 flex-shrink-0\" />\n                <span className=\"text-slate-700 font-medium\">{standard}</span>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Bottom CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"bg-gradient-to-br from-slate-50 to-blue-50 rounded-3xl p-12\">\n            <Heart className=\"h-16 w-16 text-red-500 mx-auto mb-6\" />\n            <h3 className=\"text-3xl md:text-4xl font-bold text-slate-900 mb-6\">\n              We Serve Humanity - HEALTH\n            </h3>\n            <p className=\"text-xl text-slate-700 mb-8 max-w-2xl mx-auto\">\n              Our commitment to serving humanity through health drives our dedication \n              to producing the highest quality PPE for global healthcare protection.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"bg-teal-500 hover:bg-teal-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300 shadow-lg\">\n                Learn About Our Products\n              </button>\n              <button className=\"border-2 border-teal-500 text-teal-600 hover:bg-teal-500 hover:text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300\">\n                Contact Manufacturing Team\n              </button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,wBAAwB;IAC5B;QACE,OAAO;QACP,aAAa;QACb,MAAM,wMAAA,CAAA,UAAO;QACb,OAAO;IACT;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;IACT;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;IACT;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;IACT;CACD;AAED,MAAM,mBAAmB;IACvB;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;;gCAAqD;8CACnD,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAEhD,8OAAC;4BAAE,WAAU;sCAA2D;;;;;;;;;;;;8BAM1E,8OAAC;oBAAI,WAAU;8BACZ,sBAAsB,GAAG,CAAC,CAAC,SAAS;wBACnC,MAAM,OAAO,QAAQ,IAAI;wBAEzB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,2BAA2B,EAAE,QAAQ,KAAK,CAAC,2GAA2G,CAAC;sDACtK,cAAA,8OAAC;gDAAK,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK;;;;;;8DAEhB,8OAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;2BAjBvB,QAAQ,KAAK;;;;;oBAwBxB;;;;;;8BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAMzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAqB;;;;;;8CACpC,8OAAC;oCAAI,WAAU;8CAAyB;;;;;;8CACxC,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;8BAKnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CAGnE,8OAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;sCAM1D,8OAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,UAAU,sBAC/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;4CAAK,WAAU;sDAA8B;;;;;;;mCARzC;;;;;;;;;;;;;;;;8BAeb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;0CAI7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAA+H;;;;;;kDAGjJ,8OAAC;wCAAO,WAAU;kDAAmJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnL", "debugId": null}}, {"offset": {"line": 2135, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/najam/kleverco/kleverco-weblatest/src/components/sections/businesses/investment-expertise.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { <PERSON><PERSON><PERSON>Up, BarChart3, PieChart, Target, Globe, Shield } from \"lucide-react\";\n\nconst investmentCapabilities = [\n  {\n    title: \"Global Investment Research\",\n    description: \"Comprehensive market research and analysis across international markets, providing deep insights for strategic investment decisions.\",\n    icon: Globe,\n    color: \"from-blue-500 to-indigo-500\"\n  },\n  {\n    title: \"Portfolio Management\",\n    description: \"Professional portfolio management services with focus on risk optimization and sustainable returns across diverse asset classes.\",\n    icon: Pie<PERSON>hart,\n    color: \"from-green-500 to-emerald-500\"\n  },\n  {\n    title: \"Strategic Advisory\",\n    description: \"Expert strategic advisory services for complex investment decisions, mergers, acquisitions, and business development initiatives.\",\n    icon: Target,\n    color: \"from-purple-500 to-violet-500\"\n  },\n  {\n    title: \"Risk Management\",\n    description: \"Advanced risk assessment and management frameworks ensuring prudent investment strategies and capital preservation.\",\n    icon: Shield,\n    color: \"from-red-500 to-pink-500\"\n  }\n];\n\nexport function InvestmentExpertise() {\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-slate-50 to-blue-50\">\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold text-slate-900 mb-6\">\n            Investment <span className=\"text-teal-500\">Expertise</span>\n          </h2>\n          <p className=\"text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed\">\n            As a global investment and research boutique firm, we deliver sophisticated \n            investment solutions backed by deep market expertise and rigorous analysis.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-16\">\n          {investmentCapabilities.map((capability, index) => {\n            const Icon = capability.icon;\n            \n            return (\n              <motion.div\n                key={capability.title}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"group\"\n              >\n                <div className=\"bg-white rounded-3xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 h-full\">\n                  <div className=\"flex items-start mb-6\">\n                    <div className={`w-16 h-16 bg-gradient-to-r ${capability.color} rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300`}>\n                      <Icon className=\"h-8 w-8 text-white\" />\n                    </div>\n                    <div className=\"flex-1\">\n                      <h3 className=\"text-2xl font-bold text-slate-900 group-hover:text-teal-600 transition-colors duration-300 mb-3\">\n                        {capability.title}\n                      </h3>\n                      <p className=\"text-slate-600 leading-relaxed\">\n                        {capability.description}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </motion.div>\n            );\n          })}\n        </div>\n\n        {/* Investment Approach */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"bg-white rounded-3xl p-12 shadow-lg border border-slate-100\"\n        >\n          <div className=\"text-center mb-12\">\n            <TrendingUp className=\"h-16 w-16 text-teal-500 mx-auto mb-6\" />\n            <h3 className=\"text-3xl md:text-4xl font-bold text-slate-900 mb-6\">\n              Our Investment Approach\n            </h3>\n            <p className=\"text-xl text-slate-700 max-w-2xl mx-auto\">\n              We combine rigorous research, strategic thinking, and global market insights \n              to deliver exceptional investment outcomes for our clients.\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <BarChart3 className=\"h-8 w-8 text-white\" />\n              </div>\n              <h4 className=\"text-xl font-bold text-slate-900 mb-3\">Research-Driven</h4>\n              <p className=\"text-slate-600\">\n                Deep market analysis and comprehensive research form the foundation of our investment decisions.\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Target className=\"h-8 w-8 text-white\" />\n              </div>\n              <h4 className=\"text-xl font-bold text-slate-900 mb-3\">Strategic Focus</h4>\n              <p className=\"text-slate-600\">\n                Strategic alignment with long-term objectives and sustainable value creation principles.\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-purple-500 to-violet-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Globe className=\"h-8 w-8 text-white\" />\n              </div>\n              <h4 className=\"text-xl font-bold text-slate-900 mb-3\">Global Perspective</h4>\n              <p className=\"text-slate-600\">\n                International market expertise with local insights across multiple geographic regions.\n              </p>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,yBAAyB;IAC7B;QACE,OAAO;QACP,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;IACT;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,8MAAA,CAAA,WAAQ;QACd,OAAO;IACT;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;IACT;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;;gCAAqD;8CACtD,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAE7C,8OAAC;4BAAE,WAAU;sCAA2D;;;;;;;;;;;;8BAM1E,8OAAC;oBAAI,WAAU;8BACZ,uBAAuB,GAAG,CAAC,CAAC,YAAY;wBACvC,MAAM,OAAO,WAAW,IAAI;wBAE5B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,2BAA2B,EAAE,WAAW,KAAK,CAAC,2GAA2G,CAAC;sDACzK,cAAA,8OAAC;gDAAK,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,WAAW,KAAK;;;;;;8DAEnB,8OAAC;oDAAE,WAAU;8DACV,WAAW,WAAW;;;;;;;;;;;;;;;;;;;;;;;2BAjB1B,WAAW,KAAK;;;;;oBAwB3B;;;;;;8BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CAGnE,8OAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;sCAM1D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAiB;;;;;;;;;;;;8CAIhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAiB;;;;;;;;;;;;8CAIhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C", "debugId": null}}]}