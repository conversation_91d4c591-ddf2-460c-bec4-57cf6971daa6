"use client";

import { motion } from "framer-motion";
import { Fuel, Zap, Sun, Car, Droplets, Flame } from "lucide-react";

export function EnergyProducts() {
  const products = [
    {
      name: "LPG",
      icon: Flame,
      brand: "TerraTier"
    },
    {
      name: "LNG", 
      icon: Droplets,
      brand: "TerraTier"
    },
    {
      name: "Crude Oil",
      icon: Fuel,
      brand: "TerraTier"
    },
    {
      name: "Solar panels",
      icon: Sun,
      brand: "TerraTier"
    },
    {
      name: "Solar powered geysers",
      icon: Zap,
      brand: "TerraTier"
    },
    {
      name: "EV power stations & units",
      icon: Car,
      brand: "TerraTier"
    }
  ];

  return (
    <section className="py-32 bg-slate-50">
      <div className="container mx-auto px-6">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-8 tracking-tight">
              Products we <span className="font-normal text-slate-600">trade</span>
            </h2>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto mb-8"></div>
            <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed font-light">
              We source, ship, store, transport, and deliver
            </p>
            <p className="text-lg text-slate-600 max-w-4xl mx-auto leading-relaxed font-light mt-4">
              We source the products that support our customers' businesses and help maintain the balance between the supply and demand in their respective regions. The engagements are with some national producers and with few trading houses for sourcing.
            </p>
          </motion.div>

          {/* Products Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {products.map((product, index) => {
              const Icon = product.icon;
              
              return (
                <motion.div
                  key={product.name}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-2xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300"
                >
                  <div className="text-center">
                    {/* Brand */}
                    <div className="mb-6">
                      <span className="text-sm font-medium text-slate-500 tracking-wider uppercase">
                        {product.brand}
                      </span>
                    </div>

                    {/* Icon */}
                    <div className="bg-slate-100 p-6 rounded-2xl mb-6 mx-auto w-fit">
                      <Icon className="h-12 w-12 text-slate-600" />
                    </div>

                    {/* Product Name */}
                    <h3 className="text-xl font-medium text-slate-900 mb-4">
                      {product.name}
                    </h3>

                    {/* Category Badge */}
                    <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-light bg-slate-100 text-slate-600">
                      {index < 3 ? "Hydrocarbons" : "Renewable"}
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>

          {/* Call to Action */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-center mt-20"
          >
            <div className="bg-slate-900 rounded-2xl p-12 text-white">
              <h3 className="text-3xl md:text-4xl font-light mb-6 tracking-tight">
                Global Energy <span className="font-normal text-slate-300">Solutions</span>
              </h3>
              <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-400 to-transparent mx-auto mb-8"></div>
              <p className="text-xl text-slate-300 mb-8 max-w-3xl mx-auto font-light leading-relaxed">
                From traditional hydrocarbons to cutting-edge renewable solutions, we deliver comprehensive energy products worldwide.
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
