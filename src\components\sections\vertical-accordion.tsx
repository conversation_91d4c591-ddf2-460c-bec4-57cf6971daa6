"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  Heart, 
  Cpu, 
  Zap, 
  Wheat,
  Building2,
  Plane,
  ChevronRight
} from "lucide-react";

const sectors = [
  {
    id: "healthcare",
    name: "Healthcare",
    icon: Heart,
    description: "Advancing medical innovation and patient care through strategic investments in biotechnology, pharmaceuticals, and healthcare services.",
    color: "from-red-500 to-pink-500",
    bgColor: "bg-red-50",
    stats: "50+ Portfolio Companies"
  },
  {
    id: "technology",
    name: "Technology",
    icon: Cpu,
    description: "Driving digital transformation with investments in AI, cloud computing, cybersecurity, and emerging technologies.",
    color: "from-blue-500 to-indigo-500",
    bgColor: "bg-blue-50",
    stats: "75+ Portfolio Companies"
  },
  {
    id: "energy",
    name: "Energy",
    icon: Zap,
    description: "Powering the future through renewable energy, clean technology, and sustainable energy infrastructure investments.",
    color: "from-yellow-500 to-orange-500",
    bgColor: "bg-yellow-50",
    stats: "30+ Portfolio Companies"
  },
  {
    id: "agriculture",
    name: "Agriculture",
    icon: Wheat,
    description: "Revolutionizing food production with precision agriculture, sustainable farming, and agtech innovations.",
    color: "from-green-500 to-emerald-500",
    bgColor: "bg-green-50",
    stats: "25+ Portfolio Companies"
  },
  {
    id: "real-estate",
    name: "Real Estate",
    icon: Building2,
    description: "Building tomorrow's communities through strategic real estate development and property technology investments.",
    color: "from-purple-500 to-violet-500",
    bgColor: "bg-purple-50",
    stats: "40+ Portfolio Companies"
  },
  {
    id: "aerospace",
    name: "Aerospace",
    icon: Plane,
    description: "Reaching new heights with investments in space technology, aviation innovation, and defense systems.",
    color: "from-teal-500 to-cyan-500",
    bgColor: "bg-teal-50",
    stats: "15+ Portfolio Companies"
  }
];

export function VerticalAccordion() {
  const [activePanel, setActivePanel] = useState<string | null>("healthcare");

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
            Investment <span className="text-teal-500">Sectors</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            Diversified portfolio across high-growth sectors, driving innovation and creating value through strategic partnerships.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-6 gap-4 h-[600px]">
          {sectors.map((sector, index) => {
            const Icon = sector.icon;
            const isActive = activePanel === sector.id;
            
            return (
              <motion.div
                key={sector.id}
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`relative cursor-pointer rounded-2xl overflow-hidden transition-all duration-500 ${
                  isActive ? 'lg:col-span-3' : 'lg:col-span-1'
                } ${isActive ? 'h-full' : 'h-full lg:h-full'}`}
                onClick={() => setActivePanel(isActive ? null : sector.id)}
              >
                <div className={`h-full bg-gradient-to-br ${sector.color} relative overflow-hidden`}>
                  {/* Background Pattern */}
                  <div className="absolute inset-0 bg-black/10"></div>
                  
                  {/* Content */}
                  <div className="relative z-10 h-full flex flex-col justify-between p-6 text-white">
                    <div className="flex items-center justify-between">
                      <Icon className="h-8 w-8" />
                      <ChevronRight 
                        className={`h-6 w-6 transition-transform duration-300 ${
                          isActive ? 'rotate-90' : ''
                        }`} 
                      />
                    </div>
                    
                    <div>
                      <h3 className={`font-bold mb-2 transition-all duration-300 ${
                        isActive ? 'text-2xl lg:text-3xl' : 'text-xl lg:text-lg'
                      }`}>
                        {sector.name}
                      </h3>
                      
                      <AnimatePresence>
                        {isActive && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3 }}
                            className="overflow-hidden"
                          >
                            <p className="text-white/90 mb-4 leading-relaxed">
                              {sector.description}
                            </p>
                            <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4">
                              <p className="font-semibold text-sm">
                                {sector.stats}
                              </p>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
