"use client";

import { motion } from "framer-motion";
import { TrendingUp, Building, Users, Heart, ArrowRight } from "lucide-react";
import Link from "next/link";

const businessAreas = [
  {
    title: "We Develop Business",
    subtitle: "Facilitate Business Growth",
    description: "Strategic business development and growth facilitation services for sustainable expansion.",
    icon: TrendingUp,
    color: "from-blue-500 to-indigo-500",
    href: "/businesses#develop"
  },
  {
    title: "We Support Business", 
    subtitle: "Business Innovation and Support",
    description: "Comprehensive business innovation and support services for operational excellence.",
    icon: Building,
    color: "from-green-500 to-emerald-500",
    href: "/businesses#support"
  },
  {
    title: "We Advise",
    subtitle: "Expert Financial Advice", 
    description: "Professional financial advisory services and strategic guidance for complex decisions.",
    icon: Users,
    color: "from-purple-500 to-violet-500",
    href: "/businesses#advise"
  },
  {
    title: "We Serve Humanity",
    subtitle: "HEALTH",
    description: "State-of-the-art PPE manufacturing serving global healthcare needs with highest quality standards.",
    icon: Heart,
    color: "from-red-500 to-pink-500",
    href: "/businesses#health"
  }
];

export function BusinessAreasSection() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
            Our <span className="text-teal-500">Business Areas</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Four core business verticals delivering comprehensive solutions across 
            investment, development, advisory, and healthcare sectors.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {businessAreas.map((area, index) => {
            const Icon = area.icon;
            
            return (
              <motion.div
                key={area.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <Link href={area.href}>
                  <div className="bg-gradient-to-br from-slate-50 to-blue-50 rounded-3xl p-8 border border-slate-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 h-full cursor-pointer">
                    {/* Icon */}
                    <div className={`w-16 h-16 bg-gradient-to-r ${area.color} rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className="h-8 w-8 text-white" />
                    </div>

                    {/* Content */}
                    <div className="mb-6">
                      <h3 className="text-xl font-bold text-slate-900 group-hover:text-teal-600 transition-colors duration-300 mb-2">
                        {area.title}
                      </h3>
                      <p className="text-teal-600 font-semibold text-sm mb-3">{area.subtitle}</p>
                      <p className="text-slate-600 text-sm leading-relaxed">
                        {area.description}
                      </p>
                    </div>

                    {/* CTA */}
                    <div className="flex items-center text-teal-600 font-medium text-sm group-hover:text-teal-700 transition-colors duration-300">
                      Learn More
                      <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                    </div>
                  </div>
                </Link>
              </motion.div>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-teal-600 to-blue-600 rounded-3xl p-12 text-white">
            <h3 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Partner with KleverCo?
            </h3>
            <p className="text-xl text-teal-100 mb-8 max-w-2xl mx-auto">
              Discover how our comprehensive business solutions can drive growth, 
              innovation, and success for your organization.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact">
                <button className="bg-white text-teal-600 hover:bg-teal-50 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300 shadow-lg">
                  Get in Touch
                </button>
              </Link>
              <Link href="/businesses">
                <button className="border-2 border-white text-white hover:bg-white hover:text-teal-600 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300">
                  Explore All Services
                </button>
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
