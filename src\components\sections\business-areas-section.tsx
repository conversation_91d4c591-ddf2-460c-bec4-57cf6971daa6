"use client";

import { motion } from "framer-motion";
import { TrendingUp, Building, Users, Heart, ArrowRight } from "lucide-react";
import Link from "next/link";

const businessAreas = [
  {
    title: "Healthcare",
    subtitle: "Medical Excellence",
    description: "Comprehensive healthcare solutions including telemedicine, hospital automation, PPE manufacturing, and dental services.",
    icon: Heart,
    color: "from-red-500 to-pink-500",
    href: "/businesses/healthcare"
  },
  {
    title: "Information Technology",
    subtitle: "Digital Innovation",
    description: "Advanced IT solutions through Oratier Technologies and NGT, driving digital transformation and innovation.",
    icon: Building,
    color: "from-blue-500 to-indigo-500",
    href: "/businesses/it"
  },
  {
    title: "Energy Solutions",
    subtitle: "Sustainable Power",
    description: "Comprehensive energy portfolio spanning oil & gas operations and renewable energy initiatives.",
    icon: TrendingUp,
    color: "from-green-500 to-emerald-500",
    href: "/businesses/energy"
  },
  {
    title: "Real Estate",
    subtitle: "Property Development",
    description: "Premium real estate development, tokenization solutions, and luxury property management services.",
    icon: Users,
    color: "from-purple-500 to-violet-500",
    href: "/businesses/real-estate"
  }
];

export function BusinessAreasSection() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
            Our <span className="text-teal-500">Businesses</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Comprehensive business portfolio spanning healthcare, technology, energy,
            real estate, aviation, and food sectors across global markets.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {businessAreas.map((area, index) => {
            const Icon = area.icon;
            
            return (
              <motion.div
                key={area.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <Link href={area.href}>
                  <div className="bg-gradient-to-br from-slate-50 to-blue-50 rounded-3xl p-8 border border-slate-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 h-full cursor-pointer">
                    {/* Icon */}
                    <div className={`w-16 h-16 bg-gradient-to-r ${area.color} rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className="h-8 w-8 text-white" />
                    </div>

                    {/* Content */}
                    <div className="mb-6">
                      <h3 className="text-xl font-bold text-slate-900 group-hover:text-teal-600 transition-colors duration-300 mb-2">
                        {area.title}
                      </h3>
                      <p className="text-teal-600 font-semibold text-sm mb-3">{area.subtitle}</p>
                      <p className="text-slate-600 text-sm leading-relaxed">
                        {area.description}
                      </p>
                    </div>

                    {/* CTA */}
                    <div className="flex items-center text-teal-600 font-medium text-sm group-hover:text-teal-700 transition-colors duration-300">
                      Learn More
                      <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                    </div>
                  </div>
                </Link>
              </motion.div>
            );
          })}
        </div>


      </div>
    </section>
  );
}
