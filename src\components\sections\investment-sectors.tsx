"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronDown, Heart, Building, Zap, Home, Plane, Utensils } from "lucide-react";
import Link from "next/link";

const sectors = [
  {
    id: "healthcare",
    title: "Healthcare",
    subtitle: "Medical Excellence & Innovation",
    description: "Comprehensive healthcare solutions including telemedicine serving 50M+ patients annually, hospital automation through SERS emergency systems, state-of-the-art PPE manufacturing with 100M+ gloves produced, and premium dental services through Aesthetica clinics.",
    icon: Heart,
    color: "from-red-500 to-pink-500",
    bgColor: "bg-red-50",
    stats: ["50M+ Patients", "100M+ Gloves", "3 Dental Clinics", "24/7 Emergency"],
    href: "/businesses/healthcare"
  },
  {
    id: "technology",
    title: "Information Technology",
    subtitle: "Digital Transformation & Innovation",
    description: "Advanced IT solutions through Oratier Technologies and NGT, driving digital transformation with cutting-edge software development, system integration, and technology consulting services for global enterprises.",
    icon: Building,
    color: "from-blue-500 to-indigo-500",
    bgColor: "bg-blue-50",
    stats: ["Global Reach", "Enterprise Solutions", "Digital Innovation", "Tech Consulting"],
    href: "/businesses/it"
  },
  {
    id: "energy",
    title: "Energy Solutions",
    subtitle: "Sustainable Power & Resources",
    description: "Comprehensive energy portfolio spanning traditional oil & gas operations and renewable energy initiatives through TerraTier, focusing on sustainable energy solutions and environmental responsibility.",
    icon: Zap,
    color: "from-green-500 to-emerald-500",
    bgColor: "bg-green-50",
    stats: ["Oil & Gas", "Renewable Energy", "TerraTier Products", "Sustainability"],
    href: "/businesses/energy"
  },
  {
    id: "real-estate",
    title: "Real Estate",
    subtitle: "Premium Property Development",
    description: "Premium real estate development, tokenization solutions through TerraTier, luxury property management via Vzory Design Studio, exclusive developments like The10 Ocean Edition, and comprehensive services through Pinnacle Developers.",
    icon: Home,
    color: "from-purple-500 to-violet-500",
    bgColor: "bg-purple-50",
    stats: ["Tokenization", "Luxury Properties", "Design Studio", "Ocean Edition"],
    href: "/businesses/real-estate"
  },
  {
    id: "aviation",
    title: "Aviation",
    subtitle: "Premium Helicopter Services",
    description: "Pinnacle Copters provides premium helicopter services and aviation solutions, delivering exceptional aerial transportation, logistics, and specialized aviation services with a focus on safety and excellence.",
    icon: Plane,
    color: "from-cyan-500 to-blue-500",
    bgColor: "bg-cyan-50",
    stats: ["Helicopter Fleet", "Aerial Transport", "Logistics", "Safety First"],
    href: "/businesses/helicopters"
  }
];

export function InvestmentSectors() {
  const [activeSection, setActiveSection] = useState<string | null>(null);

  const toggleSection = (sectionId: string) => {
    setActiveSection(activeSection === sectionId ? null : sectionId);
  };

  return (
    <section id="investment-sectors" className="min-h-screen bg-slate-50">
      <div className="container mx-auto px-6 py-20">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
            Investment <span className="text-blue-600">Sectors</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Comprehensive business portfolio spanning five core sectors with global reach and innovative solutions.
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto space-y-4">
          {sectors.map((sector, index) => {
            const Icon = sector.icon;
            const isActive = activeSection === sector.id;
            
            return (
              <motion.div
                key={sector.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`border border-slate-200 rounded-2xl overflow-hidden transition-all duration-300 ${
                  isActive ? "shadow-xl" : "shadow-md hover:shadow-lg"
                }`}
              >
                <button
                  onClick={() => toggleSection(sector.id)}
                  className={`w-full p-6 text-left transition-all duration-300 ${
                    isActive ? sector.bgColor : "bg-white hover:bg-slate-50"
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`w-12 h-12 bg-gradient-to-r ${sector.color} rounded-xl flex items-center justify-center`}>
                        <Icon className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-slate-900">{sector.title}</h3>
                        <p className="text-slate-600">{sector.subtitle}</p>
                      </div>
                    </div>
                    <ChevronDown 
                      className={`h-6 w-6 text-slate-400 transition-transform duration-300 ${
                        isActive ? "rotate-180" : ""
                      }`} 
                    />
                  </div>
                </button>

                <AnimatePresence>
                  {isActive && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="p-6 pt-0 bg-white">
                        <div className="grid md:grid-cols-2 gap-6">
                          <div>
                            <p className="text-slate-700 leading-relaxed mb-6">
                              {sector.description}
                            </p>
                            <Link href={sector.href}>
                              <button className={`px-6 py-3 bg-gradient-to-r ${sector.color} text-white rounded-lg font-medium hover:shadow-lg transition-all duration-300`}>
                                Explore {sector.title}
                              </button>
                            </Link>
                          </div>
                          <div>
                            <h4 className="font-semibold text-slate-900 mb-4">Key Highlights</h4>
                            <div className="grid grid-cols-2 gap-3">
                              {sector.stats.map((stat, statIndex) => (
                                <div
                                  key={statIndex}
                                  className="p-3 bg-slate-50 rounded-lg text-center"
                                >
                                  <span className="text-sm font-medium text-slate-700">{stat}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
