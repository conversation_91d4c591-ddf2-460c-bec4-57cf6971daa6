"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

const sectors = [
  {
    id: "healthcare",
    title: "Healthcare",
    description: "Medical excellence through telemedicine, PPE manufacturing, and premium dental services.",
    backgroundImage: "url('https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')",
    href: "/businesses/healthcare"
  },
  {
    id: "technology",
    title: "Information Technology",
    description: "Digital transformation through Oratier Technologies and NGT solutions.",
    backgroundImage: "url('https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=2125&q=80')",
    href: "/businesses/it"
  },
  {
    id: "energy",
    title: "Energy Solutions",
    description: "Sustainable energy through oil & gas operations and renewable initiatives.",
    backgroundImage: "url('https://images.unsplash.com/photo-1466611653911-95081537e5b7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')",
    href: "/businesses/energy"
  },
  {
    id: "real-estate",
    title: "Real Estate",
    description: "Premium property development, tokenization, and luxury management services.",
    backgroundImage: "url('https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')",
    href: "/businesses/real-estate"
  },
  {
    id: "aviation",
    title: "Aviation",
    description: "Premium helicopter services and aviation solutions through Pinnacle Copters.",
    backgroundImage: "url('https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')",
    href: "/businesses/helicopters"
  }
];

export function InvestmentSectors() {
  const [activeSection, setActiveSection] = useState<string | null>(null);

  const toggleSection = (sectionId: string) => {
    setActiveSection(activeSection === sectionId ? null : sectionId);
  };

  return (
    <section id="investment-sectors" className="h-screen flex flex-col">
      {sectors.map((sector, index) => {
        const isActive = activeSection === sector.id;
        const sectorHeight = isActive ? "flex-1" : "h-20";

        return (
          <motion.div
            key={sector.id}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className={`relative overflow-hidden transition-all duration-500 ease-in-out cursor-pointer ${sectorHeight} border-b border-white/20 last:border-b-0`}
            onClick={() => toggleSection(sector.id)}
            style={{
              backgroundImage: sector.backgroundImage,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          >
            {/* Overlay */}
            <div className="absolute inset-0 bg-black/60" />

            {/* Content */}
            <div className="relative z-10 h-full flex flex-col justify-center px-8 md:px-16">
              <AnimatePresence mode="wait">
                {!isActive ? (
                  // Collapsed State
                  <motion.div
                    key="collapsed"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                    className="flex items-center justify-between"
                  >
                    <h3 className="text-2xl md:text-3xl font-bold text-white">
                      {sector.title}
                    </h3>
                    <div className="text-white/60">
                      <ArrowRight className="h-6 w-6" />
                    </div>
                  </motion.div>
                ) : (
                  // Expanded State
                  <motion.div
                    key="expanded"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -30 }}
                    transition={{ duration: 0.4 }}
                    className="flex flex-col justify-center h-full max-w-4xl"
                  >
                    <h2 className="text-4xl md:text-6xl lg:text-7xl font-light text-white mb-6 leading-tight">
                      {sector.title}
                    </h2>
                    <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-2xl leading-relaxed">
                      {sector.description}
                    </p>
                    <Link href={sector.href}>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="inline-flex items-center px-8 py-4 bg-white/20 backdrop-blur-md border border-white/30 text-white rounded-xl font-medium hover:bg-white/30 transition-all duration-300 w-fit"
                      >
                        Explore {sector.title}
                        <ArrowRight className="ml-2 h-5 w-5" />
                      </motion.button>
                    </Link>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        );
      })}
    </section>
  );
}
