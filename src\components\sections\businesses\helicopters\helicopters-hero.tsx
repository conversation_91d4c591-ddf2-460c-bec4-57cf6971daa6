"use client";

import { motion } from "framer-motion";
import Image from "next/image";

export function HelicoptersHero() {
  return (
    <section className="relative pt-32 pb-12 overflow-hidden">
      {/* Parallax Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-fixed"
        style={{
          backgroundImage: "url('https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')"
        }}
      />

      {/* Dark Navy Overlay */}
      <div className="absolute inset-0 bg-[#0B1426]/90" />

      {/* Subtle silver pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(226,232,240,.1)_50%,transparent_75%,transparent_100%)] bg-[length:20px_20px]"></div>
      </div>

      {/* Aviation-themed floating elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          initial={{ opacity: 0, rotate: 0 }}
          animate={{ opacity: 0.1, rotate: 45 }}
          transition={{ duration: 2, ease: "easeOut" }}
          className="absolute top-16 right-16 w-32 h-32 border-2 border-slate-300 transform"
        />
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 0.08, x: 0 }}
          transition={{ duration: 2.5, ease: "easeOut" }}
          className="absolute bottom-20 left-20 w-24 h-24 bg-slate-400/30 transform rotate-45"
        />
        <motion.div
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 0.06, scale: 1 }}
          transition={{ duration: 3, ease: "easeOut" }}
          className="absolute top-1/3 left-1/3 w-16 h-16 border border-slate-300/50 rounded-full"
        />
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 0.05, y: 0 }}
          transition={{ duration: 2.8, ease: "easeOut" }}
          className="absolute bottom-1/4 right-1/3 w-10 h-10 bg-slate-400/50 transform rotate-12"
        />
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="mb-6"
            >
              <span className="text-slate-300 text-lg font-light tracking-wider uppercase">
                AVIATION EXCELLENCE
              </span>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-5xl md:text-7xl font-light text-slate-100 mb-8 tracking-tight"
            >
              HELICOPTERS
            </motion.h1>

            <motion.div
              initial={{ width: 0 }}
              animate={{ width: 96 }}
              transition={{ duration: 1, delay: 0.6 }}
              className="h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto mb-8"
            />

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="text-2xl text-slate-300 max-w-4xl mx-auto leading-relaxed font-light"
            >
              Comprehensive helicopter industry solutions through Pinnacle Copters,
              from procurement and maintenance to specialized aviation services
            </motion.p>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
