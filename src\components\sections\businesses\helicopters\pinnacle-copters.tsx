"use client";

import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight, Plane, Settings, Users, Shield } from "lucide-react";

export function PinnacleCopters() {
  const [currentSlide, setCurrentSlide] = useState(0);

  // Placeholder helicopter images - you can replace with actual images
  const helicopterImages = [
    "/images/helicopter-1.jpg",
    "/images/helicopter-2.jpg", 
    "/images/helicopter-3.jpg",
    "/images/helicopter-4.jpg",
    "/images/helicopter-5.jpg"
  ];

  const services = [
    {
      title: "Equipment Ownership",
      description: "Owning Equipment, sourcing pan globally",
      icon: Plane
    },
    {
      title: "Maintenance & Repair",
      description: "Providing Maintenance Repair, Overhauling (MRO) to the smallest spare part delivery",
      icon: Settings
    },
    {
      title: "Leasing Services",
      description: "Emergency Medical Services EMS (Air Ambulance), Search And Rescue SAR missions, Fire Fighting, Cargo movement and VIP transport",
      icon: Users
    },
    {
      title: "Training & Strategy",
      description: "From procurement to servicing to structuring to strategizing till training",
      icon: Shield
    }
  ];

  // Auto-advance slideshow
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % helicopterImages.length);
    }, 4000);
    return () => clearInterval(timer);
  }, [helicopterImages.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % helicopterImages.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + helicopterImages.length) % helicopterImages.length);
  };

  return (
    <section className="py-32 bg-white">
      <div className="container mx-auto px-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-8 tracking-tight">
              Pinnacle <span className="font-normal text-slate-600">Copters</span>
            </h2>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mx-auto mb-8"></div>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
            {/* Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="space-y-6">
                <p className="text-lg text-slate-700 leading-relaxed font-light">
                  Pinnacle Copters is an entity which offers One Stop Solution in Helicopter Industry. We are a boutique concern which caters to clientele in need from a simple to highly complex custom tailored solutions. We service from Military to Civilian to Private parties as per their specifications.
                </p>
                <p className="text-lg text-slate-700 leading-relaxed font-light">
                  We have our esteemed members with diversified long standing experience to address from procurement to servicing to structuring to strategizing till training.
                </p>
                <p className="text-lg text-slate-700 leading-relaxed font-light">
                  We begin from Owning Equipment, sourcing pan globally up to providing Maintenance Repair, Overhauling (MRO) to the smallest spare part delivery. We also provide leasing for various civilian needs like Emergency Medical Services EMS (Air Ambulance), Search And Rescue SAR missions, Fire Fighting, Cargo movement and VIP transport. We have deep market penetration over a broad range and in-depth expertise.
                </p>
              </div>
            </motion.div>

            {/* Slideshow */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="relative bg-slate-100 rounded-2xl overflow-hidden aspect-[4/3]">
                {/* Placeholder for helicopter images */}
                <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-slate-200 to-slate-300">
                  <div className="text-center">
                    <Plane className="h-24 w-24 text-slate-500 mx-auto mb-4" />
                    <p className="text-slate-600 font-light">
                      Helicopter Image {currentSlide + 1}
                    </p>
                    <p className="text-sm text-slate-500 mt-2">
                      Professional helicopter fleet showcase
                    </p>
                  </div>
                </div>

                {/* Navigation Arrows */}
                <button
                  onClick={prevSlide}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white p-2 rounded-full shadow-lg transition-all duration-300"
                >
                  <ChevronLeft className="h-6 w-6 text-slate-700" />
                </button>
                <button
                  onClick={nextSlide}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white p-2 rounded-full shadow-lg transition-all duration-300"
                >
                  <ChevronRight className="h-6 w-6 text-slate-700" />
                </button>

                {/* Slide Indicators */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  {helicopterImages.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentSlide(index)}
                      className={`w-3 h-3 rounded-full transition-all duration-300 ${
                        index === currentSlide ? "bg-white" : "bg-white/50"
                      }`}
                    />
                  ))}
                </div>
              </div>
            </motion.div>
          </div>

          {/* Services Grid */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h3 className="text-3xl font-light text-slate-900 text-center mb-16 tracking-tight">
              Our <span className="font-normal text-slate-600">Services</span>
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {services.map((service, index) => {
                const Icon = service.icon;
                
                return (
                  <motion.div
                    key={service.title}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="bg-slate-50 rounded-2xl p-8 text-center border border-slate-100 hover:shadow-lg transition-all duration-300"
                  >
                    <div className="bg-slate-900 p-4 rounded-2xl w-fit mx-auto mb-6">
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <h4 className="text-xl font-medium text-slate-900 mb-4">
                      {service.title}
                    </h4>
                    <p className="text-slate-600 font-light leading-relaxed">
                      {service.description}
                    </p>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
